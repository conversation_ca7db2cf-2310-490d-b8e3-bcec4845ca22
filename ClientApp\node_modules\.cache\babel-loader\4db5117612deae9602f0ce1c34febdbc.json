{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\AnalyticsTeaserCard.js\";\nimport React from 'react';\nimport { Box, Typography, Button, Grid, Avatar } from '@mui/material';\nimport { Analytics as AnalyticsIcon, TrendingUp as TrendingIcon, Visibility as ViewIcon, TouchApp as ClickIcon } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatBox = styled(Box)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    textAlign: 'center',\n    padding: theme.spacing(1),\n    borderRadius: theme.spacing(1),\n    background: 'linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%)',\n    border: '1px solid rgba(33, 150, 243, 0.2)'\n  };\n});\n_c = StatBox;\nconst AnalyticsTeaserCard = _ref2 => {\n  let {\n    onViewAnalytics\n  } = _ref2;\n  const mockStats = [{\n    icon: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this),\n    value: '1,234',\n    label: 'Profile Views',\n    color: '#2196F3'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(ClickIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    value: '567',\n    label: 'Link Clicks',\n    color: '#4CAF50'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(TrendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this),\n    value: '+23%',\n    label: 'Growth',\n    color: '#FF9800'\n  }];\n  const handleViewAnalytics = () => {\n    if (onViewAnalytics) {\n      onViewAnalytics();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: \"analytics_teaser\",\n    type: \"feature\",\n    title: \"\\uD83D\\uDCCA Discover Your Impact\",\n    description: \"See detailed insights about your profile performance and audience engagement.\",\n    actionText: \"View Analytics\",\n    onAction: handleViewAnalytics,\n    resetAfterDays: 7,\n    animation: \"slide\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      mt: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        mb: 2,\n        children: \"Here's a preview of what you can track:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: mockStats.map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 4,\n          children: /*#__PURE__*/_jsxDEV(StatBox, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                backgroundColor: stat.color,\n                width: 32,\n                height: 32,\n                mx: 'auto',\n                mb: 1\n              },\n              children: stat.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              color: stat.color,\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 2,\n        p: 2,\n        sx: {\n          backgroundColor: 'rgba(33, 150, 243, 0.05)',\n          borderRadius: 1,\n          border: '1px solid rgba(33, 150, 243, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Pro Tip:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 16\n          }, this), \" Regular analytics review helps you understand what content resonates with your audience and optimize your strategy.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c2 = AnalyticsTeaserCard;\nexport default AnalyticsTeaserCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"StatBox\");\n$RefreshReg$(_c2, \"AnalyticsTeaserCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Grid", "Avatar", "Analytics", "AnalyticsIcon", "TrendingUp", "TrendingIcon", "Visibility", "ViewIcon", "TouchApp", "ClickIcon", "styled", "UXCard", "jsxDEV", "_jsxDEV", "StatBox", "_ref", "theme", "textAlign", "padding", "spacing", "borderRadius", "background", "border", "_c", "AnalyticsTeaserCard", "_ref2", "onViewAnalytics", "mockStats", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "label", "color", "handleViewAnalytics", "id", "type", "title", "description", "actionText", "onAction", "resetAfterDays", "animation", "children", "mt", "variant", "mb", "container", "map", "stat", "index", "item", "xs", "sx", "backgroundColor", "width", "height", "mx", "component", "p", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/AnalyticsTeaserCard.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>, Typo<PERSON>, Button, Grid, Avatar } from '@mui/material';\nimport { \n  Analytics as AnalyticsIcon,\n  TrendingUp as TrendingIcon,\n  Visibility as ViewIcon,\n  TouchApp as ClickIcon \n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\n\nconst StatBox = styled(Box)(({ theme }) => ({\n  textAlign: 'center',\n  padding: theme.spacing(1),\n  borderRadius: theme.spacing(1),\n  background: 'linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%)',\n  border: '1px solid rgba(33, 150, 243, 0.2)',\n}));\n\nconst AnalyticsTeaserCard = ({ onViewAnalytics }) => {\n  const mockStats = [\n    {\n      icon: <ViewIcon />,\n      value: '1,234',\n      label: 'Profile Views',\n      color: '#2196F3'\n    },\n    {\n      icon: <ClickIcon />,\n      value: '567',\n      label: 'Link Clicks',\n      color: '#4CAF50'\n    },\n    {\n      icon: <TrendingIcon />,\n      value: '+23%',\n      label: 'Growth',\n      color: '#FF9800'\n    }\n  ];\n\n  const handleViewAnalytics = () => {\n    if (onViewAnalytics) {\n      onViewAnalytics();\n    }\n  };\n\n  return (\n    <UXCard\n      id=\"analytics_teaser\"\n      type=\"feature\"\n      title=\"📊 Discover Your Impact\"\n      description=\"See detailed insights about your profile performance and audience engagement.\"\n      actionText=\"View Analytics\"\n      onAction={handleViewAnalytics}\n      resetAfterDays={7}\n      animation=\"slide\"\n    >\n      <Box mt={2}>\n        <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n          Here's a preview of what you can track:\n        </Typography>\n        \n        <Grid container spacing={2}>\n          {mockStats.map((stat, index) => (\n            <Grid item xs={4} key={index}>\n              <StatBox>\n                <Avatar \n                  sx={{ \n                    backgroundColor: stat.color,\n                    width: 32,\n                    height: 32,\n                    mx: 'auto',\n                    mb: 1\n                  }}\n                >\n                  {stat.icon}\n                </Avatar>\n                <Typography variant=\"h6\" component=\"div\" color={stat.color}>\n                  {stat.value}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {stat.label}\n                </Typography>\n              </StatBox>\n            </Grid>\n          ))}\n        </Grid>\n        \n        <Box mt={2} p={2} sx={{ \n          backgroundColor: 'rgba(33, 150, 243, 0.05)',\n          borderRadius: 1,\n          border: '1px solid rgba(33, 150, 243, 0.1)'\n        }}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            💡 <strong>Pro Tip:</strong> Regular analytics review helps you understand what content resonates with your audience and optimize your strategy.\n          </Typography>\n        </Box>\n      </Box>\n    </UXCard>\n  );\n};\n\nexport default AnalyticsTeaserCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,QAAQ,eAAe;AACrE,SACEC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,YAAY,EAC1BC,UAAU,IAAIC,QAAQ,EACtBC,QAAQ,IAAIC,SAAS,QAChB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,OAAO,GAAGJ,MAAM,CAACb,GAAG,CAAC,CAACkB,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IAC1CE,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACzBC,YAAY,EAAEJ,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IAC9BE,UAAU,EAAE,oFAAoF;IAChGC,MAAM,EAAE;EACV,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GANET,OAAO;AAQb,MAAMU,mBAAmB,GAAGC,KAAA,IAAyB;EAAA,IAAxB;IAAEC;EAAgB,CAAC,GAAAD,KAAA;EAC9C,MAAME,SAAS,GAAG,CAChB;IACEC,IAAI,eAAEf,OAAA,CAACN,QAAQ;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEf,OAAA,CAACJ,SAAS;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEf,OAAA,CAACR,YAAY;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIV,eAAe,EAAE;MACnBA,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA,CAACF,MAAM;IACL0B,EAAE,EAAC,kBAAkB;IACrBC,IAAI,EAAC,SAAS;IACdC,KAAK,EAAC,mCAAyB;IAC/BC,WAAW,EAAC,+EAA+E;IAC3FC,UAAU,EAAC,gBAAgB;IAC3BC,QAAQ,EAAEN,mBAAoB;IAC9BO,cAAc,EAAE,CAAE;IAClBC,SAAS,EAAC,OAAO;IAAAC,QAAA,eAEjBhC,OAAA,CAAChB,GAAG;MAACiD,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACThC,OAAA,CAACf,UAAU;QAACiD,OAAO,EAAC,OAAO;QAACZ,KAAK,EAAC,gBAAgB;QAACa,EAAE,EAAE,CAAE;QAAAH,QAAA,EAAC;MAE1D;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnB,OAAA,CAACb,IAAI;QAACiD,SAAS;QAAC9B,OAAO,EAAE,CAAE;QAAA0B,QAAA,EACxBlB,SAAS,CAACuB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBvC,OAAA,CAACb,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACfhC,OAAA,CAACC,OAAO;YAAA+B,QAAA,gBACNhC,OAAA,CAACZ,MAAM;cACLsD,EAAE,EAAE;gBACFC,eAAe,EAAEL,IAAI,CAAChB,KAAK;gBAC3BsB,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVC,EAAE,EAAE,MAAM;gBACVX,EAAE,EAAE;cACN,CAAE;cAAAH,QAAA,EAEDM,IAAI,CAACvB;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACTnB,OAAA,CAACf,UAAU;cAACiD,OAAO,EAAC,IAAI;cAACa,SAAS,EAAC,KAAK;cAACzB,KAAK,EAAEgB,IAAI,CAAChB,KAAM;cAAAU,QAAA,EACxDM,IAAI,CAAClB;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACbnB,OAAA,CAACf,UAAU;cAACiD,OAAO,EAAC,SAAS;cAACZ,KAAK,EAAC,gBAAgB;cAAAU,QAAA,EACjDM,IAAI,CAACjB;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GAnBWoB,KAAK;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBtB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPnB,OAAA,CAAChB,GAAG;QAACiD,EAAE,EAAE,CAAE;QAACe,CAAC,EAAE,CAAE;QAACN,EAAE,EAAE;UACpBC,eAAe,EAAE,0BAA0B;UAC3CpC,YAAY,EAAE,CAAC;UACfE,MAAM,EAAE;QACV,CAAE;QAAAuB,QAAA,eACAhC,OAAA,CAACf,UAAU;UAACiD,OAAO,EAAC,SAAS;UAACZ,KAAK,EAAC,gBAAgB;UAAAU,QAAA,GAAC,eAChD,eAAAhC,OAAA;YAAAgC,QAAA,EAAQ;UAAQ;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,wHAC9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC8B,GAAA,GAlFItC,mBAAmB;AAoFzB,eAAeA,mBAAmB;AAAC,IAAAD,EAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAAxC,EAAA;AAAAwC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}