import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Ty<PERSON>graphy,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  Grid,
  LinearProgress,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Close as CloseIcon,
  Share as ShareIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Add as AddIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function SocialMediaIntegrationGuide({ socialLinks, onAddSocialLink }) {
  const [isVisible, setIsVisible] = useState(false);
  const [completionScore, setCompletionScore] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('socialIntegrationGuideVisible');
    const hasMinimalSocial = socialLinks && socialLinks.length < 3;
    
    setIsVisible(isCardVisible !== 'false' && hasMinimalSocial);
    calculateCompletionScore();
  }, [socialLinks]);

  const calculateCompletionScore = () => {
    const connectedPlatforms = socialPlatforms.filter(platform => 
      socialLinks?.some(link => link.category?.toLowerCase() === platform.name.toLowerCase())
    ).length;
    
    const score = (connectedPlatforms / socialPlatforms.length) * 100;
    setCompletionScore(Math.round(score));
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('socialIntegrationGuideVisible', 'false');
  };

  const socialPlatforms = [
    {
      name: 'LinkedIn',
      icon: '💼',
      color: '#0077B5',
      priority: 'Essential',
      benefit: 'Professional networking',
      audience: 'Business professionals',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'linkedin'),
    },
    {
      name: 'Instagram',
      icon: '📸',
      color: '#E4405F',
      priority: 'High',
      benefit: 'Visual storytelling',
      audience: 'Creative & lifestyle',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'instagram'),
    },
    {
      name: 'Twitter',
      icon: '🐦',
      color: '#1DA1F2',
      priority: 'Medium',
      benefit: 'Thought leadership',
      audience: 'Tech & news',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'twitter'),
    },
    {
      name: 'YouTube',
      icon: '📺',
      color: '#FF0000',
      priority: 'Medium',
      benefit: 'Video content',
      audience: 'Educators & creators',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'youtube'),
    },
    {
      name: 'TikTok',
      icon: '🎵',
      color: '#000000',
      priority: 'Growing',
      benefit: 'Short-form content',
      audience: 'Gen Z & millennials',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'tiktok'),
    },
    {
      name: 'GitHub',
      icon: '💻',
      color: '#333333',
      priority: 'Tech',
      benefit: 'Code portfolio',
      audience: 'Developers',
      connected: socialLinks?.some(link => link.category?.toLowerCase() === 'github'),
    },
  ];

  const connectedCount = socialPlatforms.filter(p => p.connected).length;
  const recommendedPlatforms = socialPlatforms
    .filter(p => !p.connected)
    .sort((a, b) => {
      const priorityOrder = { 'Essential': 0, 'High': 1, 'Medium': 2, 'Growing': 3, 'Tech': 4 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    })
    .slice(0, 3);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ShareIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Social Media Integration
            </Typography>
            <Chip
              label={`${connectedCount}/${socialPlatforms.length} connected`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
          </Box>

          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Social Presence Score</Typography>
              <Typography variant="body2">{completionScore}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={completionScore}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: completionScore > 50 ? '#4CAF50' : '#FFC107',
                  borderRadius: 4,
                },
              }}
            />
          </Box>

          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Recommended Platforms
          </Typography>

          <Grid container spacing={1} sx={{ mb: 3 }}>
            {recommendedPlatforms.map((platform, index) => (
              <Grid item xs={12} key={platform.name}>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: 2,
                      p: 1.5,
                      mb: 1,
                    }}
                  >
                    <Avatar
                      sx={{
                        backgroundColor: platform.color,
                        mr: 2,
                        width: 32,
                        height: 32,
                        fontSize: '14px',
                      }}
                    >
                      {platform.icon}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="subtitle2">{platform.name}</Typography>
                        <Chip
                          label={platform.priority}
                          size="small"
                          sx={{
                            backgroundColor: platform.priority === 'Essential' ? '#4CAF50' : 'rgba(255,255,255,0.2)',
                            color: 'white',
                            fontSize: '10px',
                            height: 18,
                          }}
                        />
                      </Box>
                      <Typography variant="caption" sx={{ opacity: 0.8 }}>
                        {platform.benefit} • {platform.audience}
                      </Typography>
                    </Box>
                    <Tooltip title={`Add ${platform.name}`}>
                      <IconButton
                        size="small"
                        sx={{
                          backgroundColor: 'rgba(255,255,255,0.2)',
                          color: 'white',
                          '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                        }}
                        onClick={() => {
                          if (onAddSocialLink) onAddSocialLink(platform.name);
                        }}
                      >
                        <AddIcon sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {connectedCount > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Connected Platforms
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {socialPlatforms
                  .filter(p => p.connected)
                  .map((platform) => (
                    <Badge
                      key={platform.name}
                      badgeContent={<CheckCircleIcon sx={{ fontSize: 12 }} />}
                      color="success"
                    >
                      <Chip
                        avatar={<span style={{ fontSize: '12px' }}>{platform.icon}</span>}
                        label={platform.name}
                        size="small"
                        sx={{
                          backgroundColor: 'rgba(255,255,255,0.2)',
                          color: 'white',
                        }}
                      />
                    </Badge>
                  ))}
              </Box>
            </Box>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <StarIcon sx={{ fontSize: 16 }} />
              <Typography variant="caption">
                {completionScore > 70 ? 'Excellent' : completionScore > 40 ? 'Good' : 'Getting started'} social presence
              </Typography>
            </Box>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => {
                if (onAddSocialLink) onAddSocialLink();
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Add Platform
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
