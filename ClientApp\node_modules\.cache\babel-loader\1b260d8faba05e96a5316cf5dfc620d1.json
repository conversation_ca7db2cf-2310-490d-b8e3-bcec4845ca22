{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { UXStorageManager } from '../components/UXEnhancements/UXCard';\n\n// Daily tips rotation\nconst DAILY_TIPS = [{\n  id: 'tip_profile_complete',\n  title: 'Complete Your Profile',\n  description: 'Profiles with photos and complete information get 3x more engagement!',\n  type: 'tip'\n}, {\n  id: 'tip_email_verification',\n  title: 'Verify Your Email',\n  description: 'Always keep your email updated for account recovery and important notifications.',\n  type: 'tip'\n}, {\n  id: 'tip_link_optimization',\n  title: 'Optimize Your Links',\n  description: 'Use action verbs in your link titles like \"Watch Now\", \"Join Us\", or \"Download\".',\n  type: 'tip'\n}, {\n  id: 'tip_analytics_tracking',\n  title: 'Track Your Success',\n  description: 'Check your analytics regularly to see which content performs best.',\n  type: 'tip'\n}, {\n  id: 'tip_social_connections',\n  title: 'Connect Social Media',\n  description: 'Link your social profiles to increase your online presence and credibility.',\n  type: 'tip'\n}, {\n  id: 'tip_security_2fa',\n  title: 'Enable Two-Factor Authentication',\n  description: 'Protect your account with 2FA for enhanced security.',\n  type: 'feature'\n}, {\n  id: 'tip_custom_links',\n  title: 'Create Custom Links',\n  description: 'Use memorable custom URLs to make your links more professional.',\n  type: 'tip'\n}];\n\n// Feature spotlights\nconst FEATURE_SPOTLIGHTS = [{\n  id: 'feature_analytics',\n  title: 'Discover Analytics',\n  description: 'See detailed insights about your profile views, link clicks, and audience engagement.',\n  actionText: 'View Analytics',\n  type: 'feature'\n}, {\n  id: 'feature_bundles',\n  title: 'Upgrade Your Plan',\n  description: 'Unlock premium features like advanced analytics, custom branding, and priority support.',\n  actionText: 'View Plans',\n  type: 'feature'\n}, {\n  id: 'feature_qr_codes',\n  title: 'QR Code Sharing',\n  description: 'Generate QR codes for your profile and links for easy offline sharing.',\n  actionText: 'Generate QR',\n  type: 'feature'\n}];\n\n// Inspirational examples\nconst INSPIRATION_EXAMPLES = [{\n  id: 'inspiration_portfolio',\n  title: 'Portfolio Showcase',\n  description: 'Creative professionals use iDigics to showcase their work and attract clients.',\n  chips: ['Portfolio', 'Creative', 'Freelance'],\n  type: 'inspiration'\n}, {\n  id: 'inspiration_business',\n  title: 'Business Networking',\n  description: 'Entrepreneurs share their business links and contact information efficiently.',\n  chips: ['Business', 'Networking', 'Contact'],\n  type: 'inspiration'\n}, {\n  id: 'inspiration_events',\n  title: 'Event Promotion',\n  description: 'Event organizers use custom links to promote and track event registrations.',\n  chips: ['Events', 'Marketing', 'Tracking'],\n  type: 'inspiration'\n}];\n\n// Badge system\nconst AVAILABLE_BADGES = [{\n  id: 'verified_email',\n  name: 'Verified Badge',\n  description: 'Verify your email address',\n  icon: '✅',\n  requirement: 'email_verified'\n}, {\n  id: 'first_link',\n  name: 'Link Creator',\n  description: 'Create your first custom link',\n  icon: '🔗',\n  requirement: 'links_count >= 1'\n}, {\n  id: 'profile_complete',\n  name: 'Profile Master',\n  description: 'Complete your profile 100%',\n  icon: '⭐',\n  requirement: 'profile_complete'\n}, {\n  id: 'social_connected',\n  name: 'Social Butterfly',\n  description: 'Connect 3+ social media accounts',\n  icon: '🦋',\n  requirement: 'social_links >= 3'\n}, {\n  id: 'analytics_viewer',\n  name: 'Data Analyst',\n  description: 'View your analytics 5+ times',\n  icon: '📊',\n  requirement: 'analytics_views >= 5'\n}];\nexport const useUXEnhancements = function () {\n  _s();\n  let page = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'general';\n  const [dailyTip, setDailyTip] = useState(null);\n  const [featureSpotlight, setFeatureSpotlight] = useState(null);\n  const [inspirationExample, setInspiration] = useState(null);\n  const [availableBadges, setAvailableBadges] = useState([]);\n\n  // Get daily tip based on current date\n  const getDailyTip = useCallback(() => {\n    const today = new Date();\n    const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);\n    const tipIndex = dayOfYear % DAILY_TIPS.length;\n    return DAILY_TIPS[tipIndex];\n  }, []);\n\n  // Get random feature spotlight\n  const getFeatureSpotlight = useCallback(() => {\n    const availableFeatures = FEATURE_SPOTLIGHTS.filter(feature => !UXStorageManager.isCardDismissed(feature.id, 7) // Reset after 7 days\n    );\n    if (availableFeatures.length === 0) return null;\n    const randomIndex = Math.floor(Math.random() * availableFeatures.length);\n    return availableFeatures[randomIndex];\n  }, []);\n\n  // Get random inspiration example\n  const getInspirationExample = useCallback(() => {\n    const availableExamples = INSPIRATION_EXAMPLES.filter(example => !UXStorageManager.isCardDismissed(example.id, 3) // Reset after 3 days\n    );\n    if (availableExamples.length === 0) return null;\n    const randomIndex = Math.floor(Math.random() * availableExamples.length);\n    return availableExamples[randomIndex];\n  }, []);\n\n  // Calculate profile completion percentage\n  const calculateProfileCompletion = useCallback(profile => {\n    if (!profile) return 0;\n    const fields = ['firstName', 'lastName', 'email', 'occupation', 'country', 'bio', 'avatar'];\n    const completedFields = fields.filter(field => {\n      const value = profile[field];\n      return value && value.toString().trim() !== '';\n    });\n    return Math.round(completedFields.length / fields.length * 100);\n  }, []);\n\n  // Check badge eligibility\n  const checkBadgeEligibility = useCallback(function (userProfile) {\n    let userStats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    return AVAILABLE_BADGES.filter(badge => {\n      if (UXStorageManager.isCardDismissed(`badge_${badge.id}`)) return false;\n      switch (badge.requirement) {\n        case 'email_verified':\n          return (userProfile === null || userProfile === void 0 ? void 0 : userProfile.emailVerified) === true;\n        case 'links_count >= 1':\n          return (userStats.linksCount || 0) >= 1;\n        case 'profile_complete':\n          return calculateProfileCompletion(userProfile) >= 100;\n        case 'social_links >= 3':\n          return (userStats.socialLinksCount || 0) >= 3;\n        case 'analytics_views >= 5':\n          return (userStats.analyticsViews || 0) >= 5;\n        default:\n          return false;\n      }\n    });\n  }, [calculateProfileCompletion]);\n\n  // Initialize UX enhancements\n  useEffect(() => {\n    // Set daily tip\n    const tip = getDailyTip();\n    if (tip && !UXStorageManager.isCardDismissed(tip.id, 1)) {\n      // Reset daily\n      setDailyTip(tip);\n    }\n\n    // Set feature spotlight\n    const spotlight = getFeatureSpotlight();\n    setFeatureSpotlight(spotlight);\n\n    // Set inspiration example\n    const inspiration = getInspirationExample();\n    setInspiration(inspiration);\n  }, [getDailyTip, getFeatureSpotlight, getInspirationExample]);\n\n  // Update available badges when profile changes\n  const updateBadges = useCallback((userProfile, userStats) => {\n    const eligible = checkBadgeEligibility(userProfile, userStats);\n    setAvailableBadges(eligible);\n  }, [checkBadgeEligibility]);\n\n  // Quick actions based on page\n  const getQuickActions = useCallback(page => {\n    const actions = {\n      profile: [{\n        label: 'Add Photo',\n        icon: '📷',\n        action: 'upload_photo'\n      }, {\n        label: 'Edit Bio',\n        icon: '✏️',\n        action: 'edit_bio'\n      }, {\n        label: 'Verify Email',\n        icon: '✅',\n        action: 'verify_email'\n      }, {\n        label: 'View Public Profile',\n        icon: '👁️',\n        action: 'view_profile'\n      }],\n      links: [{\n        label: 'Add New Link',\n        icon: '➕',\n        action: 'add_link'\n      }, {\n        label: 'View Analytics',\n        icon: '📊',\n        action: 'view_analytics'\n      }, {\n        label: 'Generate QR Code',\n        icon: '📱',\n        action: 'generate_qr'\n      }, {\n        label: 'Share Profile',\n        icon: '🔗',\n        action: 'share_profile'\n      }],\n      analytics: [{\n        label: 'Export Data',\n        icon: '📥',\n        action: 'export_data'\n      }, {\n        label: 'Set Goals',\n        icon: '🎯',\n        action: 'set_goals'\n      }, {\n        label: 'View Reports',\n        icon: '📈',\n        action: 'view_reports'\n      }, {\n        label: 'Compare Periods',\n        icon: '📊',\n        action: 'compare_periods'\n      }],\n      bundles: [{\n        label: 'Compare Plans',\n        icon: '⚖️',\n        action: 'compare_plans'\n      }, {\n        label: 'View Features',\n        icon: '✨',\n        action: 'view_features'\n      }, {\n        label: 'Contact Support',\n        icon: '💬',\n        action: 'contact_support'\n      }, {\n        label: 'Billing History',\n        icon: '🧾',\n        action: 'billing_history'\n      }]\n    };\n    return actions[page] || actions.profile;\n  }, []);\n  return {\n    dailyTip,\n    featureSpotlight,\n    inspirationExample,\n    availableBadges,\n    updateBadges,\n    calculateProfileCompletion,\n    getQuickActions,\n    storageManager: UXStorageManager\n  };\n};\n_s(useUXEnhancements, \"gXY24wIza+99yBWL1I7XOxSXHYM=\");\nexport default useUXEnhancements;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "UXStorageManager", "DAILY_TIPS", "id", "title", "description", "type", "FEATURE_SPOTLIGHTS", "actionText", "INSPIRATION_EXAMPLES", "chips", "AVAILABLE_BADGES", "name", "icon", "requirement", "useUXEnhancements", "_s", "page", "arguments", "length", "undefined", "dailyTip", "setDailyTip", "featureSpotlight", "setFeatureSpotlight", "<PERSON><PERSON><PERSON><PERSON>", "setInspiration", "availableBadges", "setAvailableBadges", "getDailyTip", "today", "Date", "dayOfYear", "Math", "floor", "getFullYear", "tipIndex", "getFeatureSpotlight", "availableFeatures", "filter", "feature", "isCardDismissed", "randomIndex", "random", "getInspirationExample", "availableExamples", "example", "calculateProfileCompletion", "profile", "fields", "completedFields", "field", "value", "toString", "trim", "round", "checkBadgeEligibility", "userProfile", "userStats", "badge", "emailVerified", "linksCount", "socialLinksCount", "analyticsViews", "tip", "spotlight", "inspiration", "updateBadges", "eligible", "getQuickActions", "actions", "label", "action", "links", "analytics", "bundles", "storageManager"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/hooks/useUXEnhancements.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { UXStorageManager } from '../components/UXEnhancements/UXCard';\n\n// Daily tips rotation\nconst DAILY_TIPS = [\n  {\n    id: 'tip_profile_complete',\n    title: 'Complete Your Profile',\n    description: 'Profiles with photos and complete information get 3x more engagement!',\n    type: 'tip'\n  },\n  {\n    id: 'tip_email_verification',\n    title: 'Verify Your Email',\n    description: 'Always keep your email updated for account recovery and important notifications.',\n    type: 'tip'\n  },\n  {\n    id: 'tip_link_optimization',\n    title: 'Optimize Your Links',\n    description: 'Use action verbs in your link titles like \"Watch Now\", \"Join Us\", or \"Download\".',\n    type: 'tip'\n  },\n  {\n    id: 'tip_analytics_tracking',\n    title: 'Track Your Success',\n    description: 'Check your analytics regularly to see which content performs best.',\n    type: 'tip'\n  },\n  {\n    id: 'tip_social_connections',\n    title: 'Connect Social Media',\n    description: 'Link your social profiles to increase your online presence and credibility.',\n    type: 'tip'\n  },\n  {\n    id: 'tip_security_2fa',\n    title: 'Enable Two-Factor Authentication',\n    description: 'Protect your account with 2FA for enhanced security.',\n    type: 'feature'\n  },\n  {\n    id: 'tip_custom_links',\n    title: 'Create Custom Links',\n    description: 'Use memorable custom URLs to make your links more professional.',\n    type: 'tip'\n  }\n];\n\n// Feature spotlights\nconst FEATURE_SPOTLIGHTS = [\n  {\n    id: 'feature_analytics',\n    title: 'Discover Analytics',\n    description: 'See detailed insights about your profile views, link clicks, and audience engagement.',\n    actionText: 'View Analytics',\n    type: 'feature'\n  },\n  {\n    id: 'feature_bundles',\n    title: 'Upgrade Your Plan',\n    description: 'Unlock premium features like advanced analytics, custom branding, and priority support.',\n    actionText: 'View Plans',\n    type: 'feature'\n  },\n  {\n    id: 'feature_qr_codes',\n    title: 'QR Code Sharing',\n    description: 'Generate QR codes for your profile and links for easy offline sharing.',\n    actionText: 'Generate QR',\n    type: 'feature'\n  }\n];\n\n// Inspirational examples\nconst INSPIRATION_EXAMPLES = [\n  {\n    id: 'inspiration_portfolio',\n    title: 'Portfolio Showcase',\n    description: 'Creative professionals use iDigics to showcase their work and attract clients.',\n    chips: ['Portfolio', 'Creative', 'Freelance'],\n    type: 'inspiration'\n  },\n  {\n    id: 'inspiration_business',\n    title: 'Business Networking',\n    description: 'Entrepreneurs share their business links and contact information efficiently.',\n    chips: ['Business', 'Networking', 'Contact'],\n    type: 'inspiration'\n  },\n  {\n    id: 'inspiration_events',\n    title: 'Event Promotion',\n    description: 'Event organizers use custom links to promote and track event registrations.',\n    chips: ['Events', 'Marketing', 'Tracking'],\n    type: 'inspiration'\n  }\n];\n\n// Badge system\nconst AVAILABLE_BADGES = [\n  {\n    id: 'verified_email',\n    name: 'Verified Badge',\n    description: 'Verify your email address',\n    icon: '✅',\n    requirement: 'email_verified'\n  },\n  {\n    id: 'first_link',\n    name: 'Link Creator',\n    description: 'Create your first custom link',\n    icon: '🔗',\n    requirement: 'links_count >= 1'\n  },\n  {\n    id: 'profile_complete',\n    name: 'Profile Master',\n    description: 'Complete your profile 100%',\n    icon: '⭐',\n    requirement: 'profile_complete'\n  },\n  {\n    id: 'social_connected',\n    name: 'Social Butterfly',\n    description: 'Connect 3+ social media accounts',\n    icon: '🦋',\n    requirement: 'social_links >= 3'\n  },\n  {\n    id: 'analytics_viewer',\n    name: 'Data Analyst',\n    description: 'View your analytics 5+ times',\n    icon: '📊',\n    requirement: 'analytics_views >= 5'\n  }\n];\n\nexport const useUXEnhancements = (page = 'general') => {\n  const [dailyTip, setDailyTip] = useState(null);\n  const [featureSpotlight, setFeatureSpotlight] = useState(null);\n  const [inspirationExample, setInspiration] = useState(null);\n  const [availableBadges, setAvailableBadges] = useState([]);\n\n  // Get daily tip based on current date\n  const getDailyTip = useCallback(() => {\n    const today = new Date();\n    const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);\n    const tipIndex = dayOfYear % DAILY_TIPS.length;\n    return DAILY_TIPS[tipIndex];\n  }, []);\n\n  // Get random feature spotlight\n  const getFeatureSpotlight = useCallback(() => {\n    const availableFeatures = FEATURE_SPOTLIGHTS.filter(\n      feature => !UXStorageManager.isCardDismissed(feature.id, 7) // Reset after 7 days\n    );\n    if (availableFeatures.length === 0) return null;\n    \n    const randomIndex = Math.floor(Math.random() * availableFeatures.length);\n    return availableFeatures[randomIndex];\n  }, []);\n\n  // Get random inspiration example\n  const getInspirationExample = useCallback(() => {\n    const availableExamples = INSPIRATION_EXAMPLES.filter(\n      example => !UXStorageManager.isCardDismissed(example.id, 3) // Reset after 3 days\n    );\n    if (availableExamples.length === 0) return null;\n    \n    const randomIndex = Math.floor(Math.random() * availableExamples.length);\n    return availableExamples[randomIndex];\n  }, []);\n\n  // Calculate profile completion percentage\n  const calculateProfileCompletion = useCallback((profile) => {\n    if (!profile) return 0;\n    \n    const fields = [\n      'firstName',\n      'lastName', \n      'email',\n      'occupation',\n      'country',\n      'bio',\n      'avatar'\n    ];\n    \n    const completedFields = fields.filter(field => {\n      const value = profile[field];\n      return value && value.toString().trim() !== '';\n    });\n    \n    return Math.round((completedFields.length / fields.length) * 100);\n  }, []);\n\n  // Check badge eligibility\n  const checkBadgeEligibility = useCallback((userProfile, userStats = {}) => {\n    return AVAILABLE_BADGES.filter(badge => {\n      if (UXStorageManager.isCardDismissed(`badge_${badge.id}`)) return false;\n      \n      switch (badge.requirement) {\n        case 'email_verified':\n          return userProfile?.emailVerified === true;\n        case 'links_count >= 1':\n          return (userStats.linksCount || 0) >= 1;\n        case 'profile_complete':\n          return calculateProfileCompletion(userProfile) >= 100;\n        case 'social_links >= 3':\n          return (userStats.socialLinksCount || 0) >= 3;\n        case 'analytics_views >= 5':\n          return (userStats.analyticsViews || 0) >= 5;\n        default:\n          return false;\n      }\n    });\n  }, [calculateProfileCompletion]);\n\n  // Initialize UX enhancements\n  useEffect(() => {\n    // Set daily tip\n    const tip = getDailyTip();\n    if (tip && !UXStorageManager.isCardDismissed(tip.id, 1)) { // Reset daily\n      setDailyTip(tip);\n    }\n\n    // Set feature spotlight\n    const spotlight = getFeatureSpotlight();\n    setFeatureSpotlight(spotlight);\n\n    // Set inspiration example\n    const inspiration = getInspirationExample();\n    setInspiration(inspiration);\n  }, [getDailyTip, getFeatureSpotlight, getInspirationExample]);\n\n  // Update available badges when profile changes\n  const updateBadges = useCallback((userProfile, userStats) => {\n    const eligible = checkBadgeEligibility(userProfile, userStats);\n    setAvailableBadges(eligible);\n  }, [checkBadgeEligibility]);\n\n  // Quick actions based on page\n  const getQuickActions = useCallback((page) => {\n    const actions = {\n      profile: [\n        { label: 'Add Photo', icon: '📷', action: 'upload_photo' },\n        { label: 'Edit Bio', icon: '✏️', action: 'edit_bio' },\n        { label: 'Verify Email', icon: '✅', action: 'verify_email' },\n        { label: 'View Public Profile', icon: '👁️', action: 'view_profile' }\n      ],\n      links: [\n        { label: 'Add New Link', icon: '➕', action: 'add_link' },\n        { label: 'View Analytics', icon: '📊', action: 'view_analytics' },\n        { label: 'Generate QR Code', icon: '📱', action: 'generate_qr' },\n        { label: 'Share Profile', icon: '🔗', action: 'share_profile' }\n      ],\n      analytics: [\n        { label: 'Export Data', icon: '📥', action: 'export_data' },\n        { label: 'Set Goals', icon: '🎯', action: 'set_goals' },\n        { label: 'View Reports', icon: '📈', action: 'view_reports' },\n        { label: 'Compare Periods', icon: '📊', action: 'compare_periods' }\n      ],\n      bundles: [\n        { label: 'Compare Plans', icon: '⚖️', action: 'compare_plans' },\n        { label: 'View Features', icon: '✨', action: 'view_features' },\n        { label: 'Contact Support', icon: '💬', action: 'contact_support' },\n        { label: 'Billing History', icon: '🧾', action: 'billing_history' }\n      ]\n    };\n    \n    return actions[page] || actions.profile;\n  }, []);\n\n  return {\n    dailyTip,\n    featureSpotlight,\n    inspirationExample,\n    availableBadges,\n    updateBadges,\n    calculateProfileCompletion,\n    getQuickActions,\n    storageManager: UXStorageManager\n  };\n};\n\nexport default useUXEnhancements;\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,gBAAgB,QAAQ,qCAAqC;;AAEtE;AACA,MAAMC,UAAU,GAAG,CACjB;EACEC,EAAE,EAAE,sBAAsB;EAC1BC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,uEAAuE;EACpFC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,wBAAwB;EAC5BC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE,kFAAkF;EAC/FC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,uBAAuB;EAC3BC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,kFAAkF;EAC/FC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,wBAAwB;EAC5BC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,oEAAoE;EACjFC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,wBAAwB;EAC5BC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EAAE,6EAA6E;EAC1FC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,kBAAkB;EACtBC,KAAK,EAAE,kCAAkC;EACzCC,WAAW,EAAE,sDAAsD;EACnEC,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,kBAAkB;EACtBC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,iEAAiE;EAC9EC,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,MAAMC,kBAAkB,GAAG,CACzB;EACEJ,EAAE,EAAE,mBAAmB;EACvBC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,uFAAuF;EACpGG,UAAU,EAAE,gBAAgB;EAC5BF,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,iBAAiB;EACrBC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE,yFAAyF;EACtGG,UAAU,EAAE,YAAY;EACxBF,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,kBAAkB;EACtBC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,wEAAwE;EACrFG,UAAU,EAAE,aAAa;EACzBF,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,MAAMG,oBAAoB,GAAG,CAC3B;EACEN,EAAE,EAAE,uBAAuB;EAC3BC,KAAK,EAAE,oBAAoB;EAC3BC,WAAW,EAAE,gFAAgF;EAC7FK,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;EAC7CJ,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,sBAAsB;EAC1BC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,+EAA+E;EAC5FK,KAAK,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC;EAC5CJ,IAAI,EAAE;AACR,CAAC,EACD;EACEH,EAAE,EAAE,oBAAoB;EACxBC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,6EAA6E;EAC1FK,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;EAC1CJ,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,MAAMK,gBAAgB,GAAG,CACvB;EACER,EAAE,EAAE,gBAAgB;EACpBS,IAAI,EAAE,gBAAgB;EACtBP,WAAW,EAAE,2BAA2B;EACxCQ,IAAI,EAAE,GAAG;EACTC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,YAAY;EAChBS,IAAI,EAAE,cAAc;EACpBP,WAAW,EAAE,+BAA+B;EAC5CQ,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,kBAAkB;EACtBS,IAAI,EAAE,gBAAgB;EACtBP,WAAW,EAAE,4BAA4B;EACzCQ,IAAI,EAAE,GAAG;EACTC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,kBAAkB;EACtBS,IAAI,EAAE,kBAAkB;EACxBP,WAAW,EAAE,kCAAkC;EAC/CQ,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE;AACf,CAAC,EACD;EACEX,EAAE,EAAE,kBAAkB;EACtBS,IAAI,EAAE,cAAc;EACpBP,WAAW,EAAE,8BAA8B;EAC3CQ,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE;AACf,CAAC,CACF;AAED,OAAO,MAAMC,iBAAiB,GAAG,SAAAA,CAAA,EAAsB;EAAAC,EAAA;EAAA,IAArBC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;EAChD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2B,kBAAkB,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC3D,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM+B,WAAW,GAAG7B,WAAW,CAAC,MAAM;IACpC,MAAM8B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,KAAK,GAAG,IAAIC,IAAI,CAACD,KAAK,CAACK,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACjG,MAAMC,QAAQ,GAAGJ,SAAS,GAAG9B,UAAU,CAACiB,MAAM;IAC9C,OAAOjB,UAAU,CAACkC,QAAQ,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAGrC,WAAW,CAAC,MAAM;IAC5C,MAAMsC,iBAAiB,GAAG/B,kBAAkB,CAACgC,MAAM,CACjDC,OAAO,IAAI,CAACvC,gBAAgB,CAACwC,eAAe,CAACD,OAAO,CAACrC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,IAAImC,iBAAiB,CAACnB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAE/C,MAAMuB,WAAW,GAAGT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGL,iBAAiB,CAACnB,MAAM,CAAC;IACxE,OAAOmB,iBAAiB,CAACI,WAAW,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,qBAAqB,GAAG5C,WAAW,CAAC,MAAM;IAC9C,MAAM6C,iBAAiB,GAAGpC,oBAAoB,CAAC8B,MAAM,CACnDO,OAAO,IAAI,CAAC7C,gBAAgB,CAACwC,eAAe,CAACK,OAAO,CAAC3C,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI0C,iBAAiB,CAAC1B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAE/C,MAAMuB,WAAW,GAAGT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,CAAC,CAAC,GAAGE,iBAAiB,CAAC1B,MAAM,CAAC;IACxE,OAAO0B,iBAAiB,CAACH,WAAW,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,0BAA0B,GAAG/C,WAAW,CAAEgD,OAAO,IAAK;IAC1D,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB,MAAMC,MAAM,GAAG,CACb,WAAW,EACX,UAAU,EACV,OAAO,EACP,YAAY,EACZ,SAAS,EACT,KAAK,EACL,QAAQ,CACT;IAED,MAAMC,eAAe,GAAGD,MAAM,CAACV,MAAM,CAACY,KAAK,IAAI;MAC7C,MAAMC,KAAK,GAAGJ,OAAO,CAACG,KAAK,CAAC;MAC5B,OAAOC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;IAChD,CAAC,CAAC;IAEF,OAAOrB,IAAI,CAACsB,KAAK,CAAEL,eAAe,CAAC/B,MAAM,GAAG8B,MAAM,CAAC9B,MAAM,GAAI,GAAG,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,qBAAqB,GAAGxD,WAAW,CAAC,UAACyD,WAAW,EAAqB;IAAA,IAAnBC,SAAS,GAAAxC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACpE,OAAOP,gBAAgB,CAAC4B,MAAM,CAACoB,KAAK,IAAI;MACtC,IAAI1D,gBAAgB,CAACwC,eAAe,CAAC,SAASkB,KAAK,CAACxD,EAAE,EAAE,CAAC,EAAE,OAAO,KAAK;MAEvE,QAAQwD,KAAK,CAAC7C,WAAW;QACvB,KAAK,gBAAgB;UACnB,OAAO,CAAA2C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,aAAa,MAAK,IAAI;QAC5C,KAAK,kBAAkB;UACrB,OAAO,CAACF,SAAS,CAACG,UAAU,IAAI,CAAC,KAAK,CAAC;QACzC,KAAK,kBAAkB;UACrB,OAAOd,0BAA0B,CAACU,WAAW,CAAC,IAAI,GAAG;QACvD,KAAK,mBAAmB;UACtB,OAAO,CAACC,SAAS,CAACI,gBAAgB,IAAI,CAAC,KAAK,CAAC;QAC/C,KAAK,sBAAsB;UACzB,OAAO,CAACJ,SAAS,CAACK,cAAc,IAAI,CAAC,KAAK,CAAC;QAC7C;UACE,OAAO,KAAK;MAChB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChB,0BAA0B,CAAC,CAAC;;EAEhC;EACAhD,SAAS,CAAC,MAAM;IACd;IACA,MAAMiE,GAAG,GAAGnC,WAAW,CAAC,CAAC;IACzB,IAAImC,GAAG,IAAI,CAAC/D,gBAAgB,CAACwC,eAAe,CAACuB,GAAG,CAAC7D,EAAE,EAAE,CAAC,CAAC,EAAE;MAAE;MACzDmB,WAAW,CAAC0C,GAAG,CAAC;IAClB;;IAEA;IACA,MAAMC,SAAS,GAAG5B,mBAAmB,CAAC,CAAC;IACvCb,mBAAmB,CAACyC,SAAS,CAAC;;IAE9B;IACA,MAAMC,WAAW,GAAGtB,qBAAqB,CAAC,CAAC;IAC3ClB,cAAc,CAACwC,WAAW,CAAC;EAC7B,CAAC,EAAE,CAACrC,WAAW,EAAEQ,mBAAmB,EAAEO,qBAAqB,CAAC,CAAC;;EAE7D;EACA,MAAMuB,YAAY,GAAGnE,WAAW,CAAC,CAACyD,WAAW,EAAEC,SAAS,KAAK;IAC3D,MAAMU,QAAQ,GAAGZ,qBAAqB,CAACC,WAAW,EAAEC,SAAS,CAAC;IAC9D9B,kBAAkB,CAACwC,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACZ,qBAAqB,CAAC,CAAC;;EAE3B;EACA,MAAMa,eAAe,GAAGrE,WAAW,CAAEiB,IAAI,IAAK;IAC5C,MAAMqD,OAAO,GAAG;MACdtB,OAAO,EAAE,CACP;QAAEuB,KAAK,EAAE,WAAW;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAe,CAAC,EAC1D;QAAED,KAAK,EAAE,UAAU;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAW,CAAC,EACrD;QAAED,KAAK,EAAE,cAAc;QAAE1D,IAAI,EAAE,GAAG;QAAE2D,MAAM,EAAE;MAAe,CAAC,EAC5D;QAAED,KAAK,EAAE,qBAAqB;QAAE1D,IAAI,EAAE,KAAK;QAAE2D,MAAM,EAAE;MAAe,CAAC,CACtE;MACDC,KAAK,EAAE,CACL;QAAEF,KAAK,EAAE,cAAc;QAAE1D,IAAI,EAAE,GAAG;QAAE2D,MAAM,EAAE;MAAW,CAAC,EACxD;QAAED,KAAK,EAAE,gBAAgB;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAiB,CAAC,EACjE;QAAED,KAAK,EAAE,kBAAkB;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAc,CAAC,EAChE;QAAED,KAAK,EAAE,eAAe;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAgB,CAAC,CAChE;MACDE,SAAS,EAAE,CACT;QAAEH,KAAK,EAAE,aAAa;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAc,CAAC,EAC3D;QAAED,KAAK,EAAE,WAAW;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAY,CAAC,EACvD;QAAED,KAAK,EAAE,cAAc;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAe,CAAC,EAC7D;QAAED,KAAK,EAAE,iBAAiB;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAkB,CAAC,CACpE;MACDG,OAAO,EAAE,CACP;QAAEJ,KAAK,EAAE,eAAe;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAgB,CAAC,EAC/D;QAAED,KAAK,EAAE,eAAe;QAAE1D,IAAI,EAAE,GAAG;QAAE2D,MAAM,EAAE;MAAgB,CAAC,EAC9D;QAAED,KAAK,EAAE,iBAAiB;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAkB,CAAC,EACnE;QAAED,KAAK,EAAE,iBAAiB;QAAE1D,IAAI,EAAE,IAAI;QAAE2D,MAAM,EAAE;MAAkB,CAAC;IAEvE,CAAC;IAED,OAAOF,OAAO,CAACrD,IAAI,CAAC,IAAIqD,OAAO,CAACtB,OAAO;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL3B,QAAQ;IACRE,gBAAgB;IAChBE,kBAAkB;IAClBE,eAAe;IACfwC,YAAY;IACZpB,0BAA0B;IAC1BsB,eAAe;IACfO,cAAc,EAAE3E;EAClB,CAAC;AACH,CAAC;AAACe,EAAA,CAjJWD,iBAAiB;AAmJ9B,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}