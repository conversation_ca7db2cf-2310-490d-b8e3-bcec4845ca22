{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\pages\\\\Profile.js\",\n  _this = this,\n  _s = $RefreshSig$();\nimport { useEffect, useState, useRef } from \"react\";\nimport \"./Profile.css\";\nimport { checkAuthToken } from \"../AuthenticationData.ts\";\nimport { GetProfilesFromLink } from \"../ProfileData.ts\";\nimport { toast } from \"react-toastify\";\nimport { useProfile } from \"../Context/ProfileContext\";\nimport { UseCoupon, ReserveCoupon } from \"../CouponsData.ts\";\nimport { useNavigate } from \"react-router-dom\";\nimport { PostView, PostClick } from \"../AnalyticsData.ts\";\nimport RatingDialog from \"./RatingDialog\";\nimport CheckoutReserved from \"../sections/@dashboard/Coupons/CheckoutReserved\";\nimport { Box, CircularProgress, Typography, Dialog, DialogContent, DialogTitle, IconButton } from \"@mui/material\";\nimport { Worker, Viewer } from \"@react-pdf-viewer/core\";\nimport \"@react-pdf-viewer/core/lib/styles/index.css\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport PortraitIcon from \"@mui/icons-material/Portrait\";\nimport { Helmet } from \"react-helmet-async\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _Account$contacts, _Account$profile8, _Account$profile9, _Account$profile10, _Account$profile11, _Account$profile12, _Account$profile13, _Account$profile14, _Account$profile15, _Account$firstName, _Account$lastName, _Account$contacts3;\n  const hasRun = useRef(false);\n  const {\n    profile,\n    fetchProfile\n  } = useProfile();\n  const navigate = useNavigate();\n  const [SerialKey, setSerialKey] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [Account, setAccount] = useState({\n    email: \"\",\n    firstName: \"\",\n    id: 0,\n    lastName: \"\",\n    category: \"\",\n    profile: {\n      birthDate: \"\",\n      customLinks: null,\n      gender: \"\",\n      id: 0,\n      isPremium: false,\n      occupation: \"\",\n      premium: null,\n      profileCoverPicture: \"\",\n      profilePicture: \"\",\n      profilePictureFrame: 0,\n      socialLinks: null,\n      user: null,\n      userId: 0,\n      userName: \"\",\n      country: \"\"\n    },\n    auth: {\n      id: 0,\n      password: \"\",\n      user: null,\n      userId: 8\n    },\n    contacts: [],\n    rate: 0,\n    rateCount: 0,\n    rate_Skill_QualityOfWork: 0,\n    rate_Skill_CostEffectiveness: 0,\n    rate_Skill_Timeliness: 0,\n    rate_Skill_Communication: 0,\n    rate_Skill_Agility: 0\n  });\n\n  // Component state\n  const [SocialLinks, setSocialLinks] = useState([]);\n  const [CustomLinks, setCustomLinks] = useState([]);\n  const [sameAccount, setSameAccount] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"links\");\n\n  // Modal states\n  const [openShareModal, setOpenShareModal] = useState(false);\n  const [openContactModal, setOpenContactModal] = useState(false);\n  const [openRatingDialog, setOpenRatingDialog] = useState(SerialKey ? true : false);\n  const [openReserveDialog, setOpenReserveDialog] = useState(false);\n  const [openCvDialog, setOpenCvDialog] = useState(false);\n  const cvFileRef = useRef(null);\n\n  // Tab change handler\n  const handleTabChange = newValue => {\n    setActiveTab(newValue);\n  };\n  const handleApplyCoupon = async coupon => {\n    try {\n      var _response$data;\n      // Input validation\n      if (!coupon || typeof coupon !== \"string\" || coupon.trim() === \"\") {\n        throw new Error(\"Please enter a valid coupon serial key.\");\n      }\n      const response = await ReserveCoupon(coupon.trim());\n      if (response.error) {\n        throw new Error(response.error);\n      }\n\n      // Success message\n      const successMessage = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Coupon reserved successfully!\";\n      toast.success(successMessage, {\n        position: \"top-center\",\n        autoClose: 2000\n      });\n      return response;\n    } catch (error) {\n      // Enhanced error display\n      const errorMessage = error.message || \"Failed to reserve coupon. Please try again.\";\n      toast.error(errorMessage, {\n        position: \"top-center\",\n        autoClose: 3000\n      });\n\n      // Return error for component handling\n      return {\n        error: errorMessage\n      };\n    }\n  };\n  const handleViewCount = async (profile, profileToView) => {\n    if (hasRun.current) return;\n    try {\n      const viewData = {\n        userId: profileToView.id,\n        date: new Date()\n      };\n\n      // If the user is logged in and views someone else's profile\n      if (profile && profile.id && profile.id !== profileToView.id) {\n        var _profile$profile$gend, _profile$profile, _profile$profile$coun, _profile$profile2;\n        viewData.gender = (_profile$profile$gend = (_profile$profile = profile.profile) === null || _profile$profile === void 0 ? void 0 : _profile$profile.gender) !== null && _profile$profile$gend !== void 0 ? _profile$profile$gend : null;\n        viewData.country = (_profile$profile$coun = (_profile$profile2 = profile.profile) === null || _profile$profile2 === void 0 ? void 0 : _profile$profile2.country) !== null && _profile$profile$coun !== void 0 ? _profile$profile$coun : \"Tunisia\";\n        await PostView(viewData);\n        hasRun.current = true;\n      }\n\n      // If the user is not logged in and views a profile\n      if (profile == null) {\n        await PostView(viewData);\n        hasRun.current = true;\n      }\n    } catch (error) {\n      console.error(\"Error posting view:\", error);\n    }\n  };\n  useEffect(() => {\n    const storedSerialKey = localStorage.getItem(\"serialKey\");\n    if (storedSerialKey) {\n      setSerialKey(storedSerialKey);\n      setOpenRatingDialog(true);\n    }\n    const fetchData = () => {\n      fetchProfileData();\n    };\n\n    // Initial fetch\n    fetchData();\n\n    // Execute fetch every minute - COMMENTED OUT FOR NOW\n    // const intervalId = setInterval(fetchData, 15000);\n\n    // return () => {\n    //   clearInterval(intervalId);\n    // };\n  }, [profile]);\n  useEffect(() => {\n    const runOnce = async () => {\n      try {\n        // Check if the user is authenticated\n        const isAuthenticated = checkAuthToken();\n\n        // If the user is authenticated, ensure their profile is fetched\n        if (isAuthenticated && (!profile || !profile.id)) {\n          await fetchProfile();\n        }\n        const currentPath = window.location.pathname;\n        const searchQueryPart = currentPath.substring(currentPath.lastIndexOf(\"/\") + 1);\n\n        // Fetch the profile associated with the current URL\n        const response = await GetProfilesFromLink(searchQueryPart);\n        const profileToView = response.data;\n\n        // Determine if the authenticated user matches the profile being viewed\n        if (isAuthenticated) {\n          setSameAccount(profile.id === profileToView.id);\n        } else {\n          setSameAccount(false);\n        }\n\n        // Handle view count logic\n        await handleViewCount(isAuthenticated ? profile : null, profileToView);\n      } catch (error) {\n        console.error(\"Error in runOnce:\", error);\n      }\n    };\n    runOnce();\n  }, [profile]);\n  const fetchProfileData = async () => {\n    const currentPath = window.location.pathname;\n    const searchQueryPart = currentPath.substring(currentPath.lastIndexOf(\"/\") + 1);\n    try {\n      setIsLoading(true);\n      const response = await GetProfilesFromLink(searchQueryPart);\n      if (response.error) {\n        throw new Error(response.error);\n      }\n      setAccount(response.data);\n      setSocialLinks(response.data.profile.socialLinks);\n      setCustomLinks(response.data.profile.customLinks || []);\n      setIsLoading(false);\n    } catch (error) {\n      setIsLoading(false);\n      navigate(\"/404\");\n    }\n  };\n  const handleSendRate = async data => {\n    try {\n      const response = await UseCoupon(data);\n      if (response.error) throw new Error(response.error);\n      localStorage.removeItem(\"serialKey\");\n      setSerialKey(null);\n      toast.success(\"Rate sent successfully\", {\n        position: \"top-center\",\n        autoClose: 1000\n      });\n    } catch (error) {\n      toast.error(error.message, {\n        position: \"top-center\",\n        autoClose: 1000\n      });\n    }\n    fetchProfile();\n  };\n\n  // Helper functions\n  const generateStars = function (score) {\n    let maxStars = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n    let starsHTML = [];\n    const fullStars = Math.floor(score);\n    const halfStarThreshold = 0.4;\n    const halfStar = score % 1 >= halfStarThreshold;\n    const emptyStars = maxStars - fullStars - (halfStar ? 1 : 0);\n    for (let i = 0; i < fullStars; i++) {\n      starsHTML.push( /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fa-solid fa-star\"\n      }, `full-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 22\n      }, _this));\n    }\n    if (halfStar) {\n      starsHTML.push( /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fa-solid fa-star-half-stroke\"\n      }, \"half\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, _this));\n    }\n    for (let i = 0; i < emptyStars; i++) {\n      starsHTML.push( /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fa-regular fa-star\"\n      }, `empty-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 22\n      }, _this));\n    }\n    return starsHTML;\n  };\n  const handleLinkClick = async (linkId, linkUrl, category) => {\n    // Validate URL first\n    if (!validateURL(linkUrl)) {\n      navigate(\"/404\");\n      return;\n    }\n\n    // For iOS, navigate immediately to avoid popup blocking\n    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\n      // Post analytics in background after navigation\n      if (!sameAccount) {\n        var _Account$profile$gend, _Account$profile, _Account$profile$coun, _Account$profile2;\n        PostClick({\n          linkId: linkId,\n          userId: Account.id,\n          gender: (_Account$profile$gend = (_Account$profile = Account.profile) === null || _Account$profile === void 0 ? void 0 : _Account$profile.gender) !== null && _Account$profile$gend !== void 0 ? _Account$profile$gend : null,\n          country: (_Account$profile$coun = (_Account$profile2 = Account.profile) === null || _Account$profile2 === void 0 ? void 0 : _Account$profile2.country) !== null && _Account$profile$coun !== void 0 ? _Account$profile$coun : null,\n          category: category,\n          date: new Date()\n        }).catch(console.error); // Don't await, just fire and forget\n      }\n      window.open(linkUrl, \"_blank\");\n    } else {\n      // For other devices, use the original approach with timeout\n      setTimeout(async () => {\n        if (!sameAccount) {\n          var _Account$profile$gend2, _Account$profile3, _Account$profile$coun2, _Account$profile4;\n          await PostClick({\n            linkId: linkId,\n            userId: Account.id,\n            gender: (_Account$profile$gend2 = (_Account$profile3 = Account.profile) === null || _Account$profile3 === void 0 ? void 0 : _Account$profile3.gender) !== null && _Account$profile$gend2 !== void 0 ? _Account$profile$gend2 : null,\n            country: (_Account$profile$coun2 = (_Account$profile4 = Account.profile) === null || _Account$profile4 === void 0 ? void 0 : _Account$profile4.country) !== null && _Account$profile$coun2 !== void 0 ? _Account$profile$coun2 : null,\n            category: category,\n            date: new Date()\n          });\n        }\n        window.open(linkUrl, \"_blank\");\n      }, 100);\n    }\n  };\n  const validateURL = url => {\n    try {\n      new URL(url);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  };\n  const handleContactClick = (type, value) => {\n    let href = \"#\";\n    const encodedValue = encodeURIComponent(value);\n    const cleanTel = value.replace(/[^0-9+]/g, \"\");\n    const cleanWa = value.replace(/[^0-9]/g, \"\");\n\n    // Add a small delay to ensure touch event is properly registered on iOS\n    setTimeout(() => {\n      switch (type.toLowerCase()) {\n        case \"tel\":\n        case \"phone\":\n        case \"phonenumber\":\n          href = `tel:${cleanTel}`;\n          break;\n        case \"wa\":\n        case \"whatsapp\":\n          href = `https://wa.me/${cleanWa}`;\n          // Use a more iOS-friendly approach\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\n            window.location.href = href;\n          } else {\n            window.open(href, \"_blank\");\n          }\n          return;\n        case \"mailto\":\n        case \"email\":\n          href = `mailto:${value}`;\n          break;\n        case \"gmail\":\n          href = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(value)}`;\n          // Use a more iOS-friendly approach\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\n            window.location.href = href;\n          } else {\n            window.open(href, \"_blank\");\n          }\n          return;\n        case \"map\":\n          href = `https://maps.google.com/?q=${encodedValue}`;\n          // Use a more iOS-friendly approach\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\n            window.location.href = href;\n          } else {\n            window.open(href, \"_blank\");\n          }\n          return;\n        case \"sms\":\n          href = `sms:${cleanTel}`;\n          break;\n        default:\n          href = \"#\";\n          break;\n      }\n      if (href !== \"#\") {\n        window.location.href = href;\n      }\n    }, 100); // Small delay to ensure touch event completes\n  };\n  const validContacts = ((_Account$contacts = Account.contacts) === null || _Account$contacts === void 0 ? void 0 : _Account$contacts.filter(contact => {\n    var _contact$contactInfo;\n    return contact.isPublic && !((_contact$contactInfo = contact.contactInfo) !== null && _contact$contactInfo !== void 0 && _contact$contactInfo.startsWith(\"data:application/pdf\"));\n  })) || [];\n  const hasValidContacts = validContacts.length > 0;\n  const handleDownloadVCard = async () => {\n    try {\n      var _Account$profile5, _Account$profile6, _Account$profile7, _Account$contacts2;\n      console.log(\"vCard download clicked.\");\n      let vCard = `BEGIN:VCARD\\r\\nVERSION:3.0\\r\\n`;\n      vCard += `FN:${Account.firstName} ${Account.lastName}\\r\\n`;\n      vCard += `N:${Account.lastName};${Account.firstName};;;\\r\\n`;\n      if ((_Account$profile5 = Account.profile) !== null && _Account$profile5 !== void 0 && _Account$profile5.occupation) {\n        vCard += `TITLE:${Account.profile.occupation}\\r\\n`;\n      }\n\n      // Add organization if available\n      if ((_Account$profile6 = Account.profile) !== null && _Account$profile6 !== void 0 && _Account$profile6.organization) {\n        vCard += `ORG:${Account.profile.organization}\\r\\n`;\n      }\n\n      // Handle profile picture\n      if ((_Account$profile7 = Account.profile) !== null && _Account$profile7 !== void 0 && _Account$profile7.profilePicture) {\n        try {\n          console.log(\"vCard: Fetching photo:\", Account.profile.profilePicture);\n          const response = await fetch(Account.profile.profilePicture);\n          if (response.ok) {\n            var _contentType$split$;\n            const blob = await response.blob();\n            const contentType = blob.type;\n            const imageType = (_contentType$split$ = contentType.split(\"/\")[1]) === null || _contentType$split$ === void 0 ? void 0 : _contentType$split$.toUpperCase();\n            if ([\"JPEG\", \"JPG\", \"PNG\", \"GIF\"].includes(imageType)) {\n              const reader = new FileReader();\n              reader.onload = function (e) {\n                const base64Data = e.target.result.split(\",\")[1];\n                vCard += `PHOTO;ENCODING=BASE64;TYPE=${imageType}:${base64Data}\\r\\n`;\n              };\n              reader.readAsDataURL(blob);\n            }\n          }\n        } catch (error) {\n          console.warn(\"Could not fetch profile picture for vCard:\", error);\n        }\n      }\n\n      // Add contact information\n      (_Account$contacts2 = Account.contacts) === null || _Account$contacts2 === void 0 ? void 0 : _Account$contacts2.forEach(contact => {\n        var _contact$contactInfo2, _contact$category;\n        const cleanValue = (_contact$contactInfo2 = contact.contactInfo) === null || _contact$contactInfo2 === void 0 ? void 0 : _contact$contactInfo2.trim();\n        if (!cleanValue || cleanValue.startsWith(\"data:application/pdf\")) return;\n        const cleanTel = cleanValue.replace(/[^0-9+]/g, \"\");\n        const cleanWa = cleanValue.replace(/[^0-9]/g, \"\");\n        switch ((_contact$category = contact.category) === null || _contact$category === void 0 ? void 0 : _contact$category.toLowerCase()) {\n          case \"tel\":\n          case \"phone\":\n          case \"phonenumber\":\n            vCard += `TEL;TYPE=WORK,VOICE:${cleanTel}\\r\\n`;\n            break;\n          case \"mailto\":\n          case \"email\":\n          case \"gmail\":\n            vCard += `EMAIL;TYPE=WORK:${cleanValue}\\r\\n`;\n            break;\n          case \"map\":\n          case \"location\":\n          case \"address\":\n            vCard += `ADR;TYPE=WORK:;;${cleanValue.replace(/\\n/g, \"\\\\n\")};;;;\\r\\n`;\n            vCard += `URL;TYPE=Map:https://maps.google.com/?q=${encodeURIComponent(cleanValue)}\\r\\n`;\n            break;\n          case \"wa\":\n          case \"whatsapp\":\n            vCard += `URL;TYPE=WhatsApp:https://wa.me/${cleanWa}\\r\\n`;\n            break;\n          case \"sms\":\n            vCard += `TEL;TYPE=CELL,SMS:${cleanTel}\\r\\n`;\n            break;\n          case \"website\":\n            vCard += `URL:${cleanValue}\\r\\n`;\n            break;\n        }\n      });\n\n      // Add social links\n      SocialLinks === null || SocialLinks === void 0 ? void 0 : SocialLinks.forEach(link => {\n        var _link$linkUrl;\n        if ((_link$linkUrl = link.linkUrl) !== null && _link$linkUrl !== void 0 && _link$linkUrl.trim()) {\n          vCard += `URL:${link.linkUrl.trim()}\\r\\n`;\n        }\n      });\n\n      // Add custom links\n      CustomLinks === null || CustomLinks === void 0 ? void 0 : CustomLinks.forEach(link => {\n        var _link$linkUrl2;\n        if ((_link$linkUrl2 = link.linkUrl) !== null && _link$linkUrl2 !== void 0 && _link$linkUrl2.trim()) {\n          vCard += `URL:${link.linkUrl.trim()}\\r\\n`;\n        }\n      });\n\n      // Add current profile URL\n      vCard += `URL:${window.location.href}\\r\\n`;\n\n      // Add note\n      const note = `Digital business card from iDigics.com - Connect with ${Account.firstName} ${Account.lastName}`;\n      vCard += `NOTE:${note}\\r\\n`;\n\n      // Add revision date and end\n      vCard += `REV:${new Date().toISOString().replace(/[-:.]/g, \"\")}Z\\r\\n`;\n      vCard += `END:VCARD`;\n      console.log(\"Generated vCard:\\n\", vCard);\n\n      // Create and download the vCard file\n      const blob = new Blob([vCard], {\n        type: \"text/vcard;charset=utf-8\"\n      });\n      const url = URL.createObjectURL(blob);\n      const linkElement = document.createElement(\"a\");\n      linkElement.href = url;\n      const safeFilename = `${Account.firstName}-${Account.lastName}-contact`.toLowerCase().replace(/[^a-z0-9\\-]/g, \"_\");\n      linkElement.download = `${safeFilename}.vcf`;\n      document.body.appendChild(linkElement);\n      linkElement.click();\n      document.body.removeChild(linkElement);\n      URL.revokeObjectURL(url);\n      console.log(\"vCard download triggered.\");\n      toast.success(\"Contact card downloaded successfully!\", {\n        position: \"top-center\",\n        autoClose: 2000\n      });\n    } catch (error) {\n      console.error(\"Error generating vCard:\", error);\n      toast.error(\"Failed to generate contact card\", {\n        position: \"top-center\",\n        autoClose: 2000\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: `${Account.firstName} ${Account.lastName} | IDigics Profile`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: `Connect with ${Account.firstName} ${Account.lastName}${(_Account$profile8 = Account.profile) !== null && _Account$profile8 !== void 0 && _Account$profile8.occupation ? `, ${Account.profile.occupation}` : \"\"}. View their professional profile and social links.`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:title\",\n        content: `${Account.firstName} ${Account.lastName} | IDigics Profile`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:description\",\n        content: `Connect with ${Account.firstName} ${Account.lastName}${(_Account$profile9 = Account.profile) !== null && _Account$profile9 !== void 0 && _Account$profile9.occupation ? `, ${Account.profile.occupation}` : \"\"}. View their professional profile and social links.`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:type\",\n        content: \"profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:url\",\n        content: window.location.href\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this), ((_Account$profile10 = Account.profile) === null || _Account$profile10 === void 0 ? void 0 : _Account$profile10.profilePicture) && /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:image\",\n        content: Account.profile.profilePicture\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:site_name\",\n        content: \"IDigics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:card\",\n        content: \"summary_large_image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:title\",\n        content: `${Account.firstName} ${Account.lastName} | IDigics Profile`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:description\",\n        content: `Connect with ${Account.firstName} ${Account.lastName}${(_Account$profile11 = Account.profile) !== null && _Account$profile11 !== void 0 && _Account$profile11.occupation ? `, ${Account.profile.occupation}` : \"\"}. View their professional profile and social links.`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 9\n      }, this), ((_Account$profile12 = Account.profile) === null || _Account$profile12 === void 0 ? void 0 : _Account$profile12.profilePicture) && /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:image\",\n        content: Account.profile.profilePicture\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"profile:first_name\",\n        content: Account.firstName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"profile:last_name\",\n        content: Account.lastName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), ((_Account$profile13 = Account.profile) === null || _Account$profile13 === void 0 ? void 0 : _Account$profile13.userName) && /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"profile:username\",\n        content: Account.profile.userName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `profile-page-body ${(_Account$profile14 = Account.profile) !== null && _Account$profile14 !== void 0 && _Account$profile14.profileCoverPicture ? \"has-cover\" : \"\"}`,\n      style: {\n        backgroundImage: (_Account$profile15 = Account.profile) !== null && _Account$profile15 !== void 0 && _Account$profile15.profileCoverPicture ? `url(${Account.profile.profileCoverPicture})` : \"none\"\n      },\n      children: isLoading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          minHeight: \"100vh\",\n          flexDirection: \"column\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"textSecondary\",\n          children: \"Loading profile...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Account.profile.profilePicture || `https://via.placeholder.com/150/CCCCCC/FFFFFF?text=${((_Account$firstName = Account.firstName) === null || _Account$firstName === void 0 ? void 0 : _Account$firstName.charAt(0)) || \"\"}${((_Account$lastName = Account.lastName) === null || _Account$lastName === void 0 ? void 0 : _Account$lastName.charAt(0)) || \"\"}`,\n            alt: \"Profile\",\n            className: \"profile-pic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"full-name\",\n            children: [Account.firstName, \" \", Account.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"occupation\",\n            children: Account.profile.occupation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"contact-exchange-button\",\n            onClick: () => setOpenContactModal(true),\n            \"aria-label\": \"Contact Exchange\",\n            title: \"Contact Exchange\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user-plus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"share-button\",\n            onClick: () => setOpenShareModal(true),\n            \"aria-label\": \"Share Profile\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-share-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-button ${activeTab === \"links\" ? \"active\" : \"\"}`,\n            onClick: () => handleTabChange(\"links\"),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-link\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this), \"Links\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `tab-button ${activeTab === \"about\" ? \"active\" : \"\"}`,\n            onClick: () => handleTabChange(\"about\"),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this), \"About\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `tab-content ${activeTab === \"links\" ? \"active\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Connect With Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"links-list\",\n              children: SocialLinks === null || SocialLinks === void 0 ? void 0 : SocialLinks.filter(link => link.category && ![\"tel\", \"phone\", \"phonenumber\", \"wa\", \"whatsapp\", \"mailto\", \"email\", \"sms\", \"map\", \"location\", \"address\"].includes(link.category.toLowerCase())).map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"link-item\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  onClick: e => {\n                    e.preventDefault();\n                    handleLinkClick(link.id, link.linkUrl, link.category);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: getSocialIcon(link.category)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 25\n                  }, this), link.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 23\n                }, this)\n              }, link.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this), CustomLinks && CustomLinks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"My Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"links-list\",\n              children: CustomLinks.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"link-item\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  onClick: e => {\n                    e.preventDefault();\n                    handleLinkClick(link.id, link.linkUrl, \"custom\");\n                  },\n                  children: [link.icon ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: link.icon,\n                    alt: link.title,\n                    style: {\n                      width: \"20px\",\n                      height: \"20px\",\n                      marginRight: \"15px\",\n                      borderRadius: \"3px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 29\n                  }, this), link.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 25\n                }, this)\n              }, link.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this), hasValidContacts && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Get In Touch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"links-list\",\n              children: (_Account$contacts3 = Account.contacts) === null || _Account$contacts3 === void 0 ? void 0 : _Account$contacts3.filter(contact => {\n                var _contact$contactInfo3;\n                return contact.isPublic && !((_contact$contactInfo3 = contact.contactInfo) !== null && _contact$contactInfo3 !== void 0 && _contact$contactInfo3.startsWith(\"data:application/pdf\"));\n              }).map(contact => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"link-item\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-button\",\n                  onClick: () => {\n                    handleContactClick(contact.category, contact.contactInfo);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: getContactIcon(contact.category)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 29\n                  }, this), getContactLabel(contact.category, contact.contactInfo, contact.title)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 27\n                }, this)\n              }, contact.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"vcard-button\",\n              onClick: handleDownloadVCard,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-address-card\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this), \"Save Contact (vCard)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `tab-content ${activeTab === \"about\" ? \"active\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Professional:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 21\n                }, this), \" \", Account.profile.occupation || \"Not specified\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this), Account.profile.country && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Location:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 23\n                }, this), \" \", Account.profile.country]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 21\n              }, this), Account.category && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Category:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 23\n                }, this), \" \", Account.category]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Welcome to my profile! I'm \", Account.firstName, \" \", Account.lastName, \",\", Account.profile.occupation && ` working as a ${Account.profile.occupation}`, Account.profile.country && ` based in ${Account.profile.country}`, \". Feel free to connect with me through the links above or get in touch directly.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 15\n          }, this), Account.category === \"Student\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Curriculum Vitae\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 19\n            }, this), (_Account$contacts4 => {\n              const cvContact = (_Account$contacts4 = Account.contacts) === null || _Account$contacts4 === void 0 ? void 0 : _Account$contacts4.find(contact => contact.category === \"CvFile\" && contact.isPublic);\n              if (cvContact && cvContact.contactInfo) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cv-section\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"cv-preview\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"cv-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-file-pdf cv-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"cv-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: cvContact.title || \"Curriculum V\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: \"Click to view or download the CV\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 851,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"cv-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"cv-button cv-preview-button\",\n                        onClick: () => {\n                          cvFileRef.current = cvContact.contactInfo;\n                          setOpenCvDialog(true);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-eye\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 862,\n                          columnNumber: 33\n                        }, this), \"Preview CV\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"cv-button cv-download-button\",\n                        onClick: () => {\n                          const link = document.createElement(\"a\");\n                          link.href = cvContact.contactInfo;\n                          link.download = `${Account.firstName}_${Account.lastName}_CV.pdf`;\n                          link.target = \"_blank\";\n                          document.body.appendChild(link);\n                          link.click();\n                          document.body.removeChild(link);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-download\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 33\n                        }, this), \"Download CV\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 865,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 25\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cv-section\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"no-cv-message\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info-circle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 888,\n                      columnNumber: 29\n                    }, this), \"No CV available at the moment.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 25\n                }, this);\n              }\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 17\n          }, this), Account.category !== \"Student\" && Account.category !== \"Free\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Profile Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rating\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Global Rating\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars\",\n                children: generateStars(Account.rate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                id: \"globalRatingText\",\n                children: [Account.rate.toFixed(1), \" out of 5 (\", Account.rateCount, \" \", \"reviews)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 21\n              }, this), !sameAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rating-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"rating-action-button\",\n                  onClick: () => setOpenRatingDialog(true),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-star\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 27\n                  }, this), \"Rate Me\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"rating-action-button\",\n                  onClick: () => setOpenReserveDialog(true),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-ticket-alt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 27\n                  }, this), \"Reserve Coupon\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 17\n          }, this), Account.category !== \"Student\" && Account.category !== \"Free\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Detailed Skills\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detailed-skills\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"skill-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Quality of Work\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 939,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stars\",\n                    children: generateStars(Account.rate_Skill_QualityOfWork)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"skill-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-hand-holding-usd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 947,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Cost Effectiveness\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 948,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stars\",\n                    children: generateStars(Account.rate_Skill_CostEffectiveness)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"skill-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-hourglass-half\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 956,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Timeliness\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stars\",\n                    children: generateStars(Account.rate_Skill_Timeliness)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 959,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"skill-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 965,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Communication\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 966,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stars\",\n                    children: generateStars(Account.rate_Skill_Communication)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"skill-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-rocket\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 974,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Agility\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 975,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stars\",\n                    children: generateStars(Account.rate_Skill_Agility)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Quick Connect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                id: \"profile-qrcode\",\n                children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                  ref: canvas => {\n                    if (canvas && Account.profile.userName) {\n                      // Clear any existing content\n                      const ctx = canvas.getContext(\"2d\");\n                      ctx.clearRect(0, 0, canvas.width, canvas.height);\n                      import(\"qrcode\").then(QRCode => {\n                        QRCode.toCanvas(canvas, window.location.href, {\n                          width: 150,\n                          margin: 1,\n                          color: {\n                            dark: \"#2c3e50\",\n                            light: \"#ffffff\"\n                          },\n                          errorCorrectionLevel: \"M\",\n                          type: \"image/png\",\n                          quality: 0.92,\n                          rendererOpts: {\n                            quality: 0.92\n                          }\n                        }, error => {\n                          if (error) {\n                            console.error(\"QR Code generation error:\", error);\n                            return;\n                          }\n\n                          // Add logo overlay\n                          const ctx = canvas.getContext(\"2d\");\n                          const logo = new Image();\n                          logo.crossOrigin = \"anonymous\";\n                          logo.onload = () => {\n                            // Calculate logo size and position (center of QR code)\n                            const logoSize = 30;\n                            const x = (canvas.width - logoSize) / 2;\n                            const y = (canvas.height - logoSize) / 2;\n\n                            // Draw white background circle for logo\n                            ctx.fillStyle = \"#ffffff\";\n                            ctx.beginPath();\n                            ctx.arc(x + logoSize / 2, y + logoSize / 2, logoSize / 2 + 2, 0, 2 * Math.PI);\n                            ctx.fill();\n\n                            // Draw the logo\n                            ctx.drawImage(logo, x, y, logoSize, logoSize);\n                          };\n                          logo.onerror = () => {\n                            console.log(\"Logo not found, QR code generated without logo\");\n                          };\n                          logo.src = \"/assets/idigics_logo.png\";\n                        });\n                      }).catch(error => {\n                        console.error(\"Failed to load QR code library:\", error);\n                      });\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"qr-description\",\n                children: \"Scan this QR code to quickly access my profile on any device\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 13\n        }, this), openShareModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `modal ${openShareModal ? \"is-open\" : \"\"}`,\n          id: \"share-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"close-button\",\n              onClick: () => setOpenShareModal(false),\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Share Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1096,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"share-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                title: \"Share on Facebook\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-facebook-f\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: `https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(`Check out ${Account.firstName} ${Account.lastName}'s profile!`)}`,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                title: \"Share on Twitter\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-twitter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1108,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"qrcode\",\n              children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                ref: canvas => {\n                  if (canvas && openShareModal) {\n                    // Clear any existing content\n                    const ctx = canvas.getContext(\"2d\");\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n                    import(\"qrcode\").then(QRCode => {\n                      QRCode.toCanvas(canvas, window.location.href, {\n                        width: 150,\n                        margin: 1,\n                        color: {\n                          dark: \"#2c3e50\",\n                          light: \"#ffffff\"\n                        },\n                        errorCorrectionLevel: \"M\",\n                        type: \"image/png\",\n                        quality: 0.92,\n                        rendererOpts: {\n                          quality: 0.92\n                        }\n                      }, error => {\n                        if (error) {\n                          console.error(\"QR Code generation error:\", error);\n                          return;\n                        }\n\n                        // Add logo overlay\n                        const ctx = canvas.getContext(\"2d\");\n                        const logo = new Image();\n                        logo.crossOrigin = \"anonymous\";\n                        logo.onload = () => {\n                          // Calculate logo size and position (center of QR code)\n                          const logoSize = 30;\n                          const x = (canvas.width - logoSize) / 2;\n                          const y = (canvas.height - logoSize) / 2;\n\n                          // Draw white background circle for logo\n                          ctx.fillStyle = \"#ffffff\";\n                          ctx.beginPath();\n                          ctx.arc(x + logoSize / 2, y + logoSize / 2, logoSize / 2 + 2, 0, 2 * Math.PI);\n                          ctx.fill();\n\n                          // Draw the logo\n                          ctx.drawImage(logo, x, y, logoSize, logoSize);\n                        };\n                        logo.onerror = () => {\n                          console.log(\"Logo not found, QR code generated without logo\");\n                        };\n                        logo.src = \"/assets/idigics_logo.png\";\n                      });\n                    }).catch(error => {\n                      console.error(\"Failed to load QR code library:\", error);\n                    });\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              id: \"download-qr\",\n              onClick: () => {\n                import(\"qrcode\").then(QRCode => {\n                  // Create a temporary canvas for download\n                  const tempCanvas = document.createElement(\"canvas\");\n                  QRCode.toCanvas(tempCanvas, window.location.href, {\n                    width: 300,\n                    margin: 2,\n                    color: {\n                      dark: \"#2c3e50\",\n                      light: \"#ffffff\"\n                    },\n                    errorCorrectionLevel: \"M\",\n                    type: \"image/png\",\n                    quality: 0.92,\n                    rendererOpts: {\n                      quality: 0.92\n                    }\n                  }, error => {\n                    if (error) {\n                      console.error(\"QR Code download generation error:\", error);\n                      return;\n                    }\n\n                    // Add logo overlay to download version\n                    const ctx = tempCanvas.getContext(\"2d\");\n                    const logo = new Image();\n                    logo.crossOrigin = \"anonymous\";\n                    logo.onload = () => {\n                      const logoSize = 60; // Larger for download version\n                      const x = (tempCanvas.width - logoSize) / 2;\n                      const y = (tempCanvas.height - logoSize) / 2;\n\n                      // Draw white background circle for logo\n                      ctx.fillStyle = \"#ffffff\";\n                      ctx.beginPath();\n                      ctx.arc(x + logoSize / 2, y + logoSize / 2, logoSize / 2 + 4, 0, 2 * Math.PI);\n                      ctx.fill();\n\n                      // Draw the logo\n                      ctx.drawImage(logo, x, y, logoSize, logoSize);\n\n                      // Download the canvas\n                      const url = tempCanvas.toDataURL(\"image/png\", 0.92);\n                      const link = document.createElement(\"a\");\n                      link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;\n                      link.href = url;\n                      link.click();\n                    };\n                    logo.onerror = () => {\n                      // Fallback: download without logo\n                      const url = tempCanvas.toDataURL(\"image/png\", 0.92);\n                      const link = document.createElement(\"a\");\n                      link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;\n                      link.href = url;\n                      link.click();\n                    };\n                    logo.src = \"/assets/idigics_logo.png\";\n                  });\n                }).catch(error => {\n                  console.error(\"Failed to load QR code library for download:\", error);\n                });\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-download\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 21\n              }, this), \" Download QR\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1207,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1085,\n          columnNumber: 15\n        }, this), openContactModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `modal ${openContactModal ? \"is-open\" : \"\"}`,\n          id: \"contact-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"close-button\",\n              onClick: () => setOpenContactModal(false),\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Contact Exchange\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1316,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Exchange contact information with \", Account.firstName, \" \", Account.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1318,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"contact-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-action-button\",\n                  onClick: handleDownloadVCard,\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-address-card\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 25\n                  }, this), \"Save Contact (vCard)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-action-button\",\n                  onClick: async () => {\n                    try {\n                      if (navigator.clipboard && navigator.clipboard.writeText) {\n                        await navigator.clipboard.writeText(window.location.href);\n                        toast.success(\"Profile link copied to clipboard!\", {\n                          position: \"top-center\",\n                          autoClose: 2000\n                        });\n                      } else {\n                        // Fallback for browsers that don't support clipboard API\n                        const textArea = document.createElement(\"textarea\");\n                        textArea.value = window.location.href;\n                        document.body.appendChild(textArea);\n                        textArea.select();\n                        document.execCommand(\"copy\");\n                        document.body.removeChild(textArea);\n                        toast.success(\"Profile link copied to clipboard!\", {\n                          position: \"top-center\",\n                          autoClose: 2000\n                        });\n                      }\n                    } catch (error) {\n                      console.error(\"Failed to copy to clipboard:\", error);\n                      toast.error(\"Failed to copy link. Please copy manually: \" + window.location.href, {\n                        position: \"top-center\",\n                        autoClose: 5000\n                      });\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1381,\n                    columnNumber: 25\n                  }, this), \"Copy Profile Link\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1330,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1317,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1305,\n          columnNumber: 15\n        }, this), openRatingDialog && /*#__PURE__*/_jsxDEV(RatingDialog, {\n          onClose: () => setOpenRatingDialog(false),\n          onClick: handleSendRate,\n          openDialog: openRatingDialog,\n          serialKey: SerialKey\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1392,\n          columnNumber: 15\n        }, this), openReserveDialog && /*#__PURE__*/_jsxDEV(CheckoutReserved, {\n          onApply: handleApplyCoupon,\n          ShowCouponSection: true,\n          onClose: () => setOpenReserveDialog(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1402,\n          columnNumber: 15\n        }, this), openCvDialog && /*#__PURE__*/_jsxDEV(Dialog, {\n          open: openCvDialog,\n          onClose: () => setOpenCvDialog(false),\n          fullWidth: true,\n          maxWidth: \"md\",\n          children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n            children: [\"CV Preview \", /*#__PURE__*/_jsxDEV(PortraitIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1418,\n              columnNumber: 30\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                position: \"absolute\",\n                right: 8,\n                top: 8\n              },\n              \"aria-label\": \"close\",\n              onClick: () => setOpenCvDialog(false),\n              children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: \"600px\",\n                width: \"100%\",\n                overflow: \"auto\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Worker, {\n                workerUrl: `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`,\n                children: /*#__PURE__*/_jsxDEV(Viewer, {\n                  fileUrl: cvFileRef.current,\n                  showPreviousViewOnLoad: false\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1432,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1420,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1411,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Helper functions for icons and labels\n_s(Profile, \"wLurXe74SpvIOapwH3/iZRiHsuA=\", false, function () {\n  return [useProfile, useNavigate];\n});\n_c = Profile;\nconst getSocialIcon = category => {\n  switch (category === null || category === void 0 ? void 0 : category.toLowerCase()) {\n    case \"twitter\":\n      return \"fab fa-twitter\";\n    case \"github\":\n      return \"fab fa-github\";\n    case \"instagram\":\n      return \"fab fa-instagram\";\n    case \"facebook\":\n      return \"fab fa-facebook-f\";\n    case \"linkedin\":\n      return \"fab fa-linkedin-in\";\n    case \"youtube\":\n      return \"fab fa-youtube\";\n    case \"tiktok\":\n      return \"fab fa-tiktok\";\n    case \"snapchat\":\n      return \"fab fa-snapchat-ghost\";\n    case \"pinterest\":\n      return \"fab fa-pinterest\";\n    case \"reddit\":\n      return \"fab fa-reddit\";\n    case \"discord\":\n      return \"fab fa-discord\";\n    case \"telegram\":\n      return \"fab fa-telegram\";\n    case \"whatsapp\":\n      return \"fab fa-whatsapp\";\n    case \"website\":\n    case \"blog\":\n      return \"fas fa-globe\";\n    case \"portfolio\":\n      return \"fas fa-briefcase\";\n    case \"email\":\n      return \"fas fa-envelope\";\n    default:\n      return \"fas fa-link\";\n  }\n};\nconst getContactIcon = category => {\n  switch (category === null || category === void 0 ? void 0 : category.toLowerCase()) {\n    case \"tel\":\n    case \"phone\":\n      return \"fas fa-phone\";\n    case \"wa\":\n    case \"whatsapp\":\n      return \"fab fa-whatsapp\";\n    case \"mailto\":\n    case \"email\":\n    case \"gmail\":\n      return \"fas fa-envelope\";\n    case \"map\":\n    case \"location\":\n    case \"address\":\n      return \"fas fa-map-marker-alt\";\n    case \"sms\":\n      return \"fas fa-comment-sms\";\n    case \"fax\":\n      return \"fas fa-fax\";\n    case \"website\":\n      return \"fas fa-globe\";\n    default:\n      return \"fas fa-address-book\";\n  }\n};\nconst getContactLabel = (category, contactInfo, title) => {\n  if (!contactInfo || contactInfo.startsWith(\"data:application/pdf\")) {\n    return \"\";\n  }\n  // If we have a title, display it along with the contact info\n  if (title && title.trim()) {\n    return `${title}: ${contactInfo}`;\n  }\n  switch (category === null || category === void 0 ? void 0 : category.toLowerCase()) {\n    case \"tel\":\n    case \"phone\":\n    case \"phonenumber\":\n      return contactInfo;\n    case \"wa\":\n    case \"whatsapp\":\n      return contactInfo;\n    case \"mailto\":\n    case \"email\":\n    case \"gmail\":\n      return contactInfo;\n    case \"map\":\n    case \"location\":\n    case \"address\":\n      return contactInfo;\n    case \"sms\":\n      return contactInfo;\n    case \"fax\":\n      return contactInfo;\n    case \"website\":\n      return contactInfo;\n    default:\n      return contactInfo;\n  }\n};\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["useEffect", "useState", "useRef", "checkAuthToken", "GetProfilesFromLink", "toast", "useProfile", "UseCoupon", "ReserveCoupon", "useNavigate", "PostView", "PostClick", "RatingDialog", "Checkout<PERSON>eserved", "Box", "CircularProgress", "Typography", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "IconButton", "Worker", "Viewer", "CloseIcon", "PortraitIcon", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "_Account$contacts", "_Account$profile8", "_Account$profile9", "_Account$profile10", "_Account$profile11", "_Account$profile12", "_Account$profile13", "_Account$profile14", "_Account$profile15", "_Account$firstName", "_Account$lastName", "_Account$contacts3", "<PERSON><PERSON>un", "profile", "fetchProfile", "navigate", "Ser<PERSON><PERSON><PERSON>", "setSerialKey", "isLoading", "setIsLoading", "Account", "setAccount", "email", "firstName", "id", "lastName", "category", "birthDate", "customLinks", "gender", "isPremium", "occupation", "premium", "profileCoverPicture", "profilePicture", "profilePictureFrame", "socialLinks", "user", "userId", "userName", "country", "auth", "password", "contacts", "rate", "rateCount", "rate_Skill_QualityOfWork", "rate_Skill_CostEffectiveness", "rate_Skill_Timeliness", "rate_Skill_Communication", "rate_Skill_Agility", "SocialLinks", "setSocialLinks", "CustomLinks", "setCustomLinks", "sameAccount", "setSameAccount", "activeTab", "setActiveTab", "openShareModal", "setOpenShareModal", "openContactModal", "setOpenContactModal", "openRatingDialog", "setOpenRatingDialog", "openReserveDialog", "setOpenReserveDialog", "openCvDialog", "setOpenCvDialog", "cvFileRef", "handleTabChange", "newValue", "handleApplyCoupon", "coupon", "_response$data", "trim", "Error", "response", "error", "successMessage", "data", "message", "success", "position", "autoClose", "errorMessage", "handleViewCount", "profile<PERSON><PERSON><PERSON>iew", "current", "viewData", "date", "Date", "_profile$profile$gend", "_profile$profile", "_profile$profile$coun", "_profile$profile2", "console", "storedSerialKey", "localStorage", "getItem", "fetchData", "fetchProfileData", "runOnce", "isAuthenticated", "currentPath", "window", "location", "pathname", "searchQueryPart", "substring", "lastIndexOf", "handleSendRate", "removeItem", "generateStars", "score", "maxStars", "arguments", "length", "undefined", "starsHTML", "fullStars", "Math", "floor", "halfStarThreshold", "halfStar", "emptyStars", "i", "push", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_this", "handleLinkClick", "linkId", "linkUrl", "validateURL", "navigator", "userAgent", "match", "_Account$profile$gend", "_Account$profile", "_Account$profile$coun", "_Account$profile2", "catch", "open", "setTimeout", "_Account$profile$gend2", "_Account$profile3", "_Account$profile$coun2", "_Account$profile4", "url", "URL", "e", "handleContactClick", "type", "value", "href", "encodedValue", "encodeURIComponent", "cleanTel", "replace", "cleanWa", "toLowerCase", "validContacts", "filter", "contact", "_contact$contactInfo", "isPublic", "contactInfo", "startsWith", "hasValidContacts", "handleDownloadVCard", "_Account$profile5", "_Account$profile6", "_Account$profile7", "_Account$contacts2", "log", "vCard", "organization", "fetch", "ok", "_contentType$split$", "blob", "contentType", "imageType", "split", "toUpperCase", "includes", "reader", "FileReader", "onload", "base64Data", "target", "result", "readAsDataURL", "warn", "for<PERSON>ach", "_contact$contactInfo2", "_contact$category", "cleanValue", "link", "_link$linkUrl", "_link$linkUrl2", "note", "toISOString", "Blob", "createObjectURL", "linkElement", "document", "createElement", "safeFilename", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "children", "name", "content", "property", "style", "backgroundImage", "sx", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size", "variant", "color", "src", "char<PERSON>t", "alt", "onClick", "title", "map", "preventDefault", "getSocialIcon", "icon", "width", "height", "marginRight", "borderRadius", "_contact$contactInfo3", "getContactIcon", "getContactLabel", "_Account$contacts4", "cvContact", "find", "toFixed", "ref", "canvas", "ctx", "getContext", "clearRect", "then", "QRCode", "to<PERSON><PERSON><PERSON>", "margin", "dark", "light", "errorCorrectionLevel", "quality", "rendererOpts", "logo", "Image", "crossOrigin", "logoSize", "x", "y", "fillStyle", "beginPath", "arc", "PI", "fill", "drawImage", "onerror", "rel", "tempCanvas", "toDataURL", "clipboard", "writeText", "textArea", "select", "execCommand", "onClose", "openDialog", "<PERSON><PERSON><PERSON>", "onApply", "ShowCouponSection", "fullWidth", "max<PERSON><PERSON><PERSON>", "right", "top", "overflow", "workerUrl", "fileUrl", "showPreviousViewOnLoad", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/pages/Profile.js"], "sourcesContent": ["import { useEffect, useState, useRef } from \"react\";\r\nimport \"./Profile.css\";\r\nimport { checkAuthToken } from \"../AuthenticationData.ts\";\r\nimport { GetProfilesFromLink } from \"../ProfileData.ts\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useProfile } from \"../Context/ProfileContext\";\r\nimport { UseCoupon, ReserveCoupon } from \"../CouponsData.ts\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { PostView, PostClick } from \"../AnalyticsData.ts\";\r\nimport RatingDialog from \"./RatingDialog\";\r\nimport CheckoutReserved from \"../sections/@dashboard/Coupons/CheckoutReserved\";\r\nimport {\r\n  Box,\r\n  CircularProgress,\r\n  Typography,\r\n  Dialog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { Worker, Viewer } from \"@react-pdf-viewer/core\";\r\nimport \"@react-pdf-viewer/core/lib/styles/index.css\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport PortraitIcon from \"@mui/icons-material/Portrait\";\r\nimport { Helmet } from \"react-helmet-async\";\r\n\r\nconst Profile = () => {\r\n  const hasRun = useRef(false);\r\n  const { profile, fetchProfile } = useProfile();\r\n  const navigate = useNavigate();\r\n  const [SerialKey, setSerialKey] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [Account, setAccount] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    id: 0,\r\n    lastName: \"\",\r\n    category: \"\",\r\n    profile: {\r\n      birthDate: \"\",\r\n      customLinks: null,\r\n      gender: \"\",\r\n      id: 0,\r\n      isPremium: false,\r\n      occupation: \"\",\r\n      premium: null,\r\n      profileCoverPicture: \"\",\r\n      profilePicture: \"\",\r\n      profilePictureFrame: 0,\r\n      socialLinks: null,\r\n      user: null,\r\n      userId: 0,\r\n      userName: \"\",\r\n      country: \"\",\r\n    },\r\n    auth: {\r\n      id: 0,\r\n      password: \"\",\r\n      user: null,\r\n      userId: 8,\r\n    },\r\n    contacts: [],\r\n    rate: 0,\r\n    rateCount: 0,\r\n    rate_Skill_QualityOfWork: 0,\r\n    rate_Skill_CostEffectiveness: 0,\r\n    rate_Skill_Timeliness: 0,\r\n    rate_Skill_Communication: 0,\r\n    rate_Skill_Agility: 0,\r\n  });\r\n\r\n  // Component state\r\n  const [SocialLinks, setSocialLinks] = useState([]);\r\n  const [CustomLinks, setCustomLinks] = useState([]);\r\n  const [sameAccount, setSameAccount] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"links\");\r\n\r\n  // Modal states\r\n  const [openShareModal, setOpenShareModal] = useState(false);\r\n  const [openContactModal, setOpenContactModal] = useState(false);\r\n  const [openRatingDialog, setOpenRatingDialog] = useState(\r\n    SerialKey ? true : false\r\n  );\r\n  const [openReserveDialog, setOpenReserveDialog] = useState(false);\r\n  const [openCvDialog, setOpenCvDialog] = useState(false);\r\n  const cvFileRef = useRef(null);\r\n\r\n  // Tab change handler\r\n  const handleTabChange = (newValue) => {\r\n    setActiveTab(newValue);\r\n  };\r\n\r\n  const handleApplyCoupon = async (coupon) => {\r\n    try {\r\n      // Input validation\r\n      if (!coupon || typeof coupon !== \"string\" || coupon.trim() === \"\") {\r\n        throw new Error(\"Please enter a valid coupon serial key.\");\r\n      }\r\n\r\n      const response = await ReserveCoupon(coupon.trim());\r\n\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // Success message\r\n      const successMessage =\r\n        response.data?.message || \"Coupon reserved successfully!\";\r\n      toast.success(successMessage, {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n\r\n      return response;\r\n    } catch (error) {\r\n      // Enhanced error display\r\n      const errorMessage =\r\n        error.message || \"Failed to reserve coupon. Please try again.\";\r\n      toast.error(errorMessage, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n\r\n      // Return error for component handling\r\n      return { error: errorMessage };\r\n    }\r\n  };\r\n\r\n  const handleViewCount = async (profile, profileToView) => {\r\n    if (hasRun.current) return;\r\n    try {\r\n      const viewData = {\r\n        userId: profileToView.id,\r\n        date: new Date(),\r\n      };\r\n\r\n      // If the user is logged in and views someone else's profile\r\n      if (profile && profile.id && profile.id !== profileToView.id) {\r\n        viewData.gender = profile.profile?.gender ?? null;\r\n        viewData.country = profile.profile?.country ?? \"Tunisia\";\r\n        await PostView(viewData);\r\n        hasRun.current = true;\r\n      }\r\n\r\n      // If the user is not logged in and views a profile\r\n      if (profile == null) {\r\n        await PostView(viewData);\r\n        hasRun.current = true;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error posting view:\", error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const storedSerialKey = localStorage.getItem(\"serialKey\");\r\n    if (storedSerialKey) {\r\n      setSerialKey(storedSerialKey);\r\n      setOpenRatingDialog(true);\r\n    }\r\n    const fetchData = () => {\r\n      fetchProfileData();\r\n    };\r\n\r\n    // Initial fetch\r\n    fetchData();\r\n\r\n    // Execute fetch every minute - COMMENTED OUT FOR NOW\r\n    // const intervalId = setInterval(fetchData, 15000);\r\n\r\n    // return () => {\r\n    //   clearInterval(intervalId);\r\n    // };\r\n  }, [profile]);\r\n\r\n  useEffect(() => {\r\n    const runOnce = async () => {\r\n      try {\r\n        // Check if the user is authenticated\r\n        const isAuthenticated = checkAuthToken();\r\n\r\n        // If the user is authenticated, ensure their profile is fetched\r\n        if (isAuthenticated && (!profile || !profile.id)) {\r\n          await fetchProfile();\r\n        }\r\n\r\n        const currentPath = window.location.pathname;\r\n        const searchQueryPart = currentPath.substring(\r\n          currentPath.lastIndexOf(\"/\") + 1\r\n        );\r\n\r\n        // Fetch the profile associated with the current URL\r\n        const response = await GetProfilesFromLink(searchQueryPart);\r\n        const profileToView = response.data;\r\n\r\n        // Determine if the authenticated user matches the profile being viewed\r\n        if (isAuthenticated) {\r\n          setSameAccount(profile.id === profileToView.id);\r\n        } else {\r\n          setSameAccount(false);\r\n        }\r\n\r\n        // Handle view count logic\r\n        await handleViewCount(isAuthenticated ? profile : null, profileToView);\r\n      } catch (error) {\r\n        console.error(\"Error in runOnce:\", error);\r\n      }\r\n    };\r\n\r\n    runOnce();\r\n  }, [profile]);\r\n\r\n  const fetchProfileData = async () => {\r\n    const currentPath = window.location.pathname;\r\n    const searchQueryPart = currentPath.substring(\r\n      currentPath.lastIndexOf(\"/\") + 1\r\n    );\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await GetProfilesFromLink(searchQueryPart);\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      setAccount(response.data);\r\n      setSocialLinks(response.data.profile.socialLinks);\r\n      setCustomLinks(response.data.profile.customLinks || []);\r\n      setIsLoading(false);\r\n    } catch (error) {\r\n      setIsLoading(false);\r\n      navigate(\"/404\");\r\n    }\r\n  };\r\n\r\n  const handleSendRate = async (data) => {\r\n    try {\r\n      const response = await UseCoupon(data);\r\n      if (response.error) throw new Error(response.error);\r\n\r\n      localStorage.removeItem(\"serialKey\");\r\n      setSerialKey(null);\r\n      toast.success(\"Rate sent successfully\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } catch (error) {\r\n      toast.error(error.message, {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    }\r\n    fetchProfile();\r\n  };\r\n\r\n  // Helper functions\r\n  const generateStars = (score, maxStars = 5) => {\r\n    let starsHTML = [];\r\n    const fullStars = Math.floor(score);\r\n    const halfStarThreshold = 0.4;\r\n    const halfStar = score % 1 >= halfStarThreshold;\r\n    const emptyStars = maxStars - fullStars - (halfStar ? 1 : 0);\r\n\r\n    for (let i = 0; i < fullStars; i++) {\r\n      starsHTML.push(<i key={`full-${i}`} className=\"fa-solid fa-star\"></i>);\r\n    }\r\n    if (halfStar) {\r\n      starsHTML.push(\r\n        <i key=\"half\" className=\"fa-solid fa-star-half-stroke\"></i>\r\n      );\r\n    }\r\n    for (let i = 0; i < emptyStars; i++) {\r\n      starsHTML.push(<i key={`empty-${i}`} className=\"fa-regular fa-star\"></i>);\r\n    }\r\n\r\n    return starsHTML;\r\n  };\r\n\r\n  const handleLinkClick = async (linkId, linkUrl, category) => {\r\n    // Validate URL first\r\n    if (!validateURL(linkUrl)) {\r\n      navigate(\"/404\");\r\n      return;\r\n    }\r\n\r\n    // For iOS, navigate immediately to avoid popup blocking\r\n    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n      // Post analytics in background after navigation\r\n      if (!sameAccount) {\r\n        PostClick({\r\n          linkId: linkId,\r\n          userId: Account.id,\r\n          gender: Account.profile?.gender ?? null,\r\n          country: Account.profile?.country ?? null,\r\n          category: category,\r\n          date: new Date(),\r\n        }).catch(console.error); // Don't await, just fire and forget\r\n      }\r\n      window.open(linkUrl, \"_blank\");\r\n    } else {\r\n      // For other devices, use the original approach with timeout\r\n      setTimeout(async () => {\r\n        if (!sameAccount) {\r\n          await PostClick({\r\n            linkId: linkId,\r\n            userId: Account.id,\r\n            gender: Account.profile?.gender ?? null,\r\n            country: Account.profile?.country ?? null,\r\n            category: category,\r\n            date: new Date(),\r\n          });\r\n        }\r\n        window.open(linkUrl, \"_blank\");\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  const validateURL = (url) => {\r\n    try {\r\n      new URL(url);\r\n      return true;\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  const handleContactClick = (type, value) => {\r\n    let href = \"#\";\r\n    const encodedValue = encodeURIComponent(value);\r\n    const cleanTel = value.replace(/[^0-9+]/g, \"\");\r\n    const cleanWa = value.replace(/[^0-9]/g, \"\");\r\n\r\n    // Add a small delay to ensure touch event is properly registered on iOS\r\n    setTimeout(() => {\r\n      switch (type.toLowerCase()) {\r\n        case \"tel\":\r\n        case \"phone\":\r\n        case \"phonenumber\":\r\n          href = `tel:${cleanTel}`;\r\n          break;\r\n        case \"wa\":\r\n        case \"whatsapp\":\r\n          href = `https://wa.me/${cleanWa}`;\r\n          // Use a more iOS-friendly approach\r\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n            window.location.href = href;\r\n          } else {\r\n            window.open(href, \"_blank\");\r\n          }\r\n          return;\r\n        case \"mailto\":\r\n        case \"email\":\r\n          href = `mailto:${value}`;\r\n          break;\r\n        case \"gmail\":\r\n          href = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(\r\n            value\r\n          )}`;\r\n          // Use a more iOS-friendly approach\r\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n            window.location.href = href;\r\n          } else {\r\n            window.open(href, \"_blank\");\r\n          }\r\n          return;\r\n        case \"map\":\r\n          href = `https://maps.google.com/?q=${encodedValue}`;\r\n          // Use a more iOS-friendly approach\r\n          if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n            window.location.href = href;\r\n          } else {\r\n            window.open(href, \"_blank\");\r\n          }\r\n          return;\r\n        case \"sms\":\r\n          href = `sms:${cleanTel}`;\r\n          break;\r\n        default:\r\n          href = \"#\";\r\n          break;\r\n      }\r\n\r\n      if (href !== \"#\") {\r\n        window.location.href = href;\r\n      }\r\n    }, 100); // Small delay to ensure touch event completes\r\n  };\r\n\r\n  const validContacts =\r\n    Account.contacts?.filter(\r\n      (contact) =>\r\n        contact.isPublic &&\r\n        !contact.contactInfo?.startsWith(\"data:application/pdf\")\r\n    ) || [];\r\n\r\n  const hasValidContacts = validContacts.length > 0;\r\n\r\n  const handleDownloadVCard = async () => {\r\n    try {\r\n      console.log(\"vCard download clicked.\");\r\n\r\n      let vCard = `BEGIN:VCARD\\r\\nVERSION:3.0\\r\\n`;\r\n      vCard += `FN:${Account.firstName} ${Account.lastName}\\r\\n`;\r\n      vCard += `N:${Account.lastName};${Account.firstName};;;\\r\\n`;\r\n\r\n      if (Account.profile?.occupation) {\r\n        vCard += `TITLE:${Account.profile.occupation}\\r\\n`;\r\n      }\r\n\r\n      // Add organization if available\r\n      if (Account.profile?.organization) {\r\n        vCard += `ORG:${Account.profile.organization}\\r\\n`;\r\n      }\r\n\r\n      // Handle profile picture\r\n      if (Account.profile?.profilePicture) {\r\n        try {\r\n          console.log(\"vCard: Fetching photo:\", Account.profile.profilePicture);\r\n          const response = await fetch(Account.profile.profilePicture);\r\n          if (response.ok) {\r\n            const blob = await response.blob();\r\n            const contentType = blob.type;\r\n            const imageType = contentType.split(\"/\")[1]?.toUpperCase();\r\n\r\n            if ([\"JPEG\", \"JPG\", \"PNG\", \"GIF\"].includes(imageType)) {\r\n              const reader = new FileReader();\r\n              reader.onload = function (e) {\r\n                const base64Data = e.target.result.split(\",\")[1];\r\n                vCard += `PHOTO;ENCODING=BASE64;TYPE=${imageType}:${base64Data}\\r\\n`;\r\n              };\r\n              reader.readAsDataURL(blob);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.warn(\"Could not fetch profile picture for vCard:\", error);\r\n        }\r\n      }\r\n\r\n      // Add contact information\r\n      Account.contacts?.forEach((contact) => {\r\n        const cleanValue = contact.contactInfo?.trim();\r\n        if (!cleanValue || cleanValue.startsWith(\"data:application/pdf\"))\r\n          return;\r\n\r\n        const cleanTel = cleanValue.replace(/[^0-9+]/g, \"\");\r\n        const cleanWa = cleanValue.replace(/[^0-9]/g, \"\");\r\n\r\n        switch (contact.category?.toLowerCase()) {\r\n          case \"tel\":\r\n          case \"phone\":\r\n          case \"phonenumber\":\r\n            vCard += `TEL;TYPE=WORK,VOICE:${cleanTel}\\r\\n`;\r\n            break;\r\n          case \"mailto\":\r\n          case \"email\":\r\n          case \"gmail\":\r\n            vCard += `EMAIL;TYPE=WORK:${cleanValue}\\r\\n`;\r\n            break;\r\n          case \"map\":\r\n          case \"location\":\r\n          case \"address\":\r\n            vCard += `ADR;TYPE=WORK:;;${cleanValue.replace(\r\n              /\\n/g,\r\n              \"\\\\n\"\r\n            )};;;;\\r\\n`;\r\n            vCard += `URL;TYPE=Map:https://maps.google.com/?q=${encodeURIComponent(\r\n              cleanValue\r\n            )}\\r\\n`;\r\n            break;\r\n          case \"wa\":\r\n          case \"whatsapp\":\r\n            vCard += `URL;TYPE=WhatsApp:https://wa.me/${cleanWa}\\r\\n`;\r\n            break;\r\n          case \"sms\":\r\n            vCard += `TEL;TYPE=CELL,SMS:${cleanTel}\\r\\n`;\r\n            break;\r\n          case \"website\":\r\n            vCard += `URL:${cleanValue}\\r\\n`;\r\n            break;\r\n        }\r\n      });\r\n\r\n      // Add social links\r\n      SocialLinks?.forEach((link) => {\r\n        if (link.linkUrl?.trim()) {\r\n          vCard += `URL:${link.linkUrl.trim()}\\r\\n`;\r\n        }\r\n      });\r\n\r\n      // Add custom links\r\n      CustomLinks?.forEach((link) => {\r\n        if (link.linkUrl?.trim()) {\r\n          vCard += `URL:${link.linkUrl.trim()}\\r\\n`;\r\n        }\r\n      });\r\n\r\n      // Add current profile URL\r\n      vCard += `URL:${window.location.href}\\r\\n`;\r\n\r\n      // Add note\r\n      const note = `Digital business card from iDigics.com - Connect with ${Account.firstName} ${Account.lastName}`;\r\n      vCard += `NOTE:${note}\\r\\n`;\r\n\r\n      // Add revision date and end\r\n      vCard += `REV:${new Date().toISOString().replace(/[-:.]/g, \"\")}Z\\r\\n`;\r\n      vCard += `END:VCARD`;\r\n\r\n      console.log(\"Generated vCard:\\n\", vCard);\r\n\r\n      // Create and download the vCard file\r\n      const blob = new Blob([vCard], { type: \"text/vcard;charset=utf-8\" });\r\n      const url = URL.createObjectURL(blob);\r\n      const linkElement = document.createElement(\"a\");\r\n      linkElement.href = url;\r\n\r\n      const safeFilename = `${Account.firstName}-${Account.lastName}-contact`\r\n        .toLowerCase()\r\n        .replace(/[^a-z0-9\\-]/g, \"_\");\r\n      linkElement.download = `${safeFilename}.vcf`;\r\n\r\n      document.body.appendChild(linkElement);\r\n      linkElement.click();\r\n      document.body.removeChild(linkElement);\r\n      URL.revokeObjectURL(url);\r\n\r\n      console.log(\"vCard download triggered.\");\r\n\r\n      toast.success(\"Contact card downloaded successfully!\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error generating vCard:\", error);\r\n      toast.error(\"Failed to generate contact card\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>{`${Account.firstName} ${Account.lastName} | IDigics Profile`}</title>\r\n        <meta\r\n          name=\"description\"\r\n          content={`Connect with ${Account.firstName} ${Account.lastName}${\r\n            Account.profile?.occupation ? `, ${Account.profile.occupation}` : \"\"\r\n          }. View their professional profile and social links.`}\r\n        />\r\n\r\n        {/* Open Graph meta tags */}\r\n        <meta\r\n          property=\"og:title\"\r\n          content={`${Account.firstName} ${Account.lastName} | IDigics Profile`}\r\n        />\r\n        <meta\r\n          property=\"og:description\"\r\n          content={`Connect with ${Account.firstName} ${Account.lastName}${\r\n            Account.profile?.occupation ? `, ${Account.profile.occupation}` : \"\"\r\n          }. View their professional profile and social links.`}\r\n        />\r\n        <meta property=\"og:type\" content=\"profile\" />\r\n        <meta property=\"og:url\" content={window.location.href} />\r\n        {Account.profile?.profilePicture && (\r\n          <meta property=\"og:image\" content={Account.profile.profilePicture} />\r\n        )}\r\n        <meta property=\"og:site_name\" content=\"IDigics\" />\r\n\r\n        {/* Twitter Card meta tags */}\r\n        <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n        <meta\r\n          name=\"twitter:title\"\r\n          content={`${Account.firstName} ${Account.lastName} | IDigics Profile`}\r\n        />\r\n        <meta\r\n          name=\"twitter:description\"\r\n          content={`Connect with ${Account.firstName} ${Account.lastName}${\r\n            Account.profile?.occupation ? `, ${Account.profile.occupation}` : \"\"\r\n          }. View their professional profile and social links.`}\r\n        />\r\n        {Account.profile?.profilePicture && (\r\n          <meta name=\"twitter:image\" content={Account.profile.profilePicture} />\r\n        )}\r\n\r\n        {/* Additional profile-specific meta tags */}\r\n        <meta property=\"profile:first_name\" content={Account.firstName} />\r\n        <meta property=\"profile:last_name\" content={Account.lastName} />\r\n        {Account.profile?.userName && (\r\n          <meta\r\n            property=\"profile:username\"\r\n            content={Account.profile.userName}\r\n          />\r\n        )}\r\n      </Helmet>\r\n\r\n      <div\r\n        className={`profile-page-body ${\r\n          Account.profile?.profileCoverPicture ? \"has-cover\" : \"\"\r\n        }`}\r\n        style={{\r\n          backgroundImage: Account.profile?.profileCoverPicture\r\n            ? `url(${Account.profile.profileCoverPicture})`\r\n            : \"none\",\r\n        }}\r\n      >\r\n        {isLoading ? (\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              alignItems: \"center\",\r\n              minHeight: \"100vh\",\r\n              flexDirection: \"column\",\r\n              gap: 2,\r\n            }}\r\n          >\r\n            <CircularProgress size={60} />\r\n            <Typography variant=\"h6\" color=\"textSecondary\">\r\n              Loading profile...\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <div className=\"profile-container\">\r\n            {/* Profile Header Section */}\r\n            <div className=\"profile-header\">\r\n              <img\r\n                src={\r\n                  Account.profile.profilePicture ||\r\n                  `https://via.placeholder.com/150/CCCCCC/FFFFFF?text=${\r\n                    Account.firstName?.charAt(0) || \"\"\r\n                  }${Account.lastName?.charAt(0) || \"\"}`\r\n                }\r\n                alt=\"Profile\"\r\n                className=\"profile-pic\"\r\n              />\r\n              <h1 className=\"full-name\">\r\n                {Account.firstName} {Account.lastName}\r\n              </h1>\r\n              <p className=\"occupation\">{Account.profile.occupation}</p>\r\n\r\n              {/* Contact Exchange Button */}\r\n              <button\r\n                className=\"contact-exchange-button\"\r\n                onClick={() => setOpenContactModal(true)}\r\n                aria-label=\"Contact Exchange\"\r\n                title=\"Contact Exchange\"\r\n              >\r\n                <i className=\"fas fa-user-plus\"></i>\r\n              </button>\r\n\r\n              {/* Share Button */}\r\n              <button\r\n                className=\"share-button\"\r\n                onClick={() => setOpenShareModal(true)}\r\n                aria-label=\"Share Profile\"\r\n              >\r\n                <i className=\"fas fa-share-alt\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            {/* Tab Navigation Buttons */}\r\n            <div className=\"tab-buttons\">\r\n              <button\r\n                className={`tab-button ${\r\n                  activeTab === \"links\" ? \"active\" : \"\"\r\n                }`}\r\n                onClick={() => handleTabChange(\"links\")}\r\n              >\r\n                <i className=\"fas fa-link\"></i>Links\r\n              </button>\r\n              <button\r\n                className={`tab-button ${\r\n                  activeTab === \"about\" ? \"active\" : \"\"\r\n                }`}\r\n                onClick={() => handleTabChange(\"about\")}\r\n              >\r\n                <i className=\"fas fa-user\"></i>About\r\n              </button>\r\n            </div>\r\n\r\n            {/* Links Tab Content */}\r\n            <div\r\n              className={`tab-content ${activeTab === \"links\" ? \"active\" : \"\"}`}\r\n            >\r\n              <div className=\"section\">\r\n                <h3>Connect With Me</h3>\r\n                <ul className=\"links-list\">\r\n                  {SocialLinks?.filter(\r\n                    (link) =>\r\n                      link.category &&\r\n                      ![\r\n                        \"tel\",\r\n                        \"phone\",\r\n                        \"phonenumber\",\r\n                        \"wa\",\r\n                        \"whatsapp\",\r\n                        \"mailto\",\r\n                        \"email\",\r\n                        \"sms\",\r\n                        \"map\",\r\n                        \"location\",\r\n                        \"address\",\r\n                      ].includes(link.category.toLowerCase())\r\n                  ).map((link) => (\r\n                    <li key={link.id} className=\"link-item\">\r\n                      <a\r\n                        href=\"#\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          handleLinkClick(link.id, link.linkUrl, link.category);\r\n                        }}\r\n                      >\r\n                        <i className={getSocialIcon(link.category)}></i>\r\n                        {link.title}\r\n                      </a>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Custom Links Section */}\r\n              {CustomLinks && CustomLinks.length > 0 && (\r\n                <div className=\"section\">\r\n                  <h3>My Links</h3>\r\n                  <ul className=\"links-list\">\r\n                    {CustomLinks.map((link) => (\r\n                      <li key={link.id} className=\"link-item\">\r\n                        <a\r\n                          href=\"#\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            handleLinkClick(link.id, link.linkUrl, \"custom\");\r\n                          }}\r\n                        >\r\n                          {link.icon ? (\r\n                            <img\r\n                              src={link.icon}\r\n                              alt={link.title}\r\n                              style={{\r\n                                width: \"20px\",\r\n                                height: \"20px\",\r\n                                marginRight: \"15px\",\r\n                                borderRadius: \"3px\",\r\n                              }}\r\n                            />\r\n                          ) : (\r\n                            <i className=\"fas fa-link\"></i>\r\n                          )}\r\n                          {link.title}\r\n                        </a>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              )}\r\n              {hasValidContacts && (\r\n                <div className=\"section\">\r\n                  <h3>Get In Touch</h3>\r\n                  <ul className=\"links-list\">\r\n                    {Account.contacts\r\n                      ?.filter(\r\n                        (contact) =>\r\n                          contact.isPublic &&\r\n                          !contact.contactInfo?.startsWith(\r\n                            \"data:application/pdf\"\r\n                          )\r\n                      )\r\n                      .map((contact) => (\r\n                        <li key={contact.id} className=\"link-item\">\r\n                          <button\r\n                            className=\"contact-button\"\r\n                            onClick={() => {\r\n                              handleContactClick(\r\n                                contact.category,\r\n                                contact.contactInfo\r\n                              );\r\n                            }}\r\n                          >\r\n                            <i className={getContactIcon(contact.category)}></i>\r\n                            {getContactLabel(\r\n                              contact.category,\r\n                              contact.contactInfo,\r\n                              contact.title\r\n                            )}\r\n                          </button>\r\n                        </li>\r\n                      ))}\r\n                  </ul>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"section\">\r\n                <button className=\"vcard-button\" onClick={handleDownloadVCard}>\r\n                  <i className=\"fas fa-address-card\"></i>\r\n                  Save Contact (vCard)\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* About Tab Content */}\r\n            <div\r\n              className={`tab-content ${activeTab === \"about\" ? \"active\" : \"\"}`}\r\n            >\r\n              <div className=\"section\">\r\n                <div className=\"about-content\">\r\n                  <p>\r\n                    <strong>Professional:</strong>{\" \"}\r\n                    {Account.profile.occupation || \"Not specified\"}\r\n                  </p>\r\n                  {Account.profile.country && (\r\n                    <p>\r\n                      <strong>Location:</strong> {Account.profile.country}\r\n                    </p>\r\n                  )}\r\n                  {Account.category && (\r\n                    <p>\r\n                      <strong>Category:</strong> {Account.category}\r\n                    </p>\r\n                  )}\r\n                  <p>\r\n                    Welcome to my profile! I'm {Account.firstName}{\" \"}\r\n                    {Account.lastName},\r\n                    {Account.profile.occupation &&\r\n                      ` working as a ${Account.profile.occupation}`}\r\n                    {Account.profile.country &&\r\n                      ` based in ${Account.profile.country}`}\r\n                    . Feel free to connect with me through the links above or\r\n                    get in touch directly.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* CV Section for Students */}\r\n              {Account.category === \"Student\" && (\r\n                <div className=\"section\">\r\n                  <h3>Curriculum Vitae</h3>\r\n                  {(() => {\r\n                    const cvContact = Account.contacts?.find(\r\n                      (contact) =>\r\n                        contact.category === \"CvFile\" && contact.isPublic\r\n                    );\r\n\r\n                    if (cvContact && cvContact.contactInfo) {\r\n                      return (\r\n                        <div className=\"cv-section\">\r\n                          <div className=\"cv-preview\">\r\n                            <div className=\"cv-header\">\r\n                              <i className=\"fas fa-file-pdf cv-icon\"></i>\r\n                              <div className=\"cv-info\">\r\n                                <h4>{cvContact.title || \"Curriculum V\"}</h4>\r\n                                <p>Click to view or download the CV</p>\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"cv-buttons\">\r\n                              <button\r\n                                className=\"cv-button cv-preview-button\"\r\n                                onClick={() => {\r\n                                  cvFileRef.current = cvContact.contactInfo;\r\n                                  setOpenCvDialog(true);\r\n                                }}\r\n                              >\r\n                                <i className=\"fas fa-eye\"></i>\r\n                                Preview CV\r\n                              </button>\r\n                              <button\r\n                                className=\"cv-button cv-download-button\"\r\n                                onClick={() => {\r\n                                  const link = document.createElement(\"a\");\r\n                                  link.href = cvContact.contactInfo;\r\n                                  link.download = `${Account.firstName}_${Account.lastName}_CV.pdf`;\r\n                                  link.target = \"_blank\";\r\n                                  document.body.appendChild(link);\r\n                                  link.click();\r\n                                  document.body.removeChild(link);\r\n                                }}\r\n                              >\r\n                                <i className=\"fas fa-download\"></i>\r\n                                Download CV\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    } else {\r\n                      return (\r\n                        <div className=\"cv-section\">\r\n                          <p className=\"no-cv-message\">\r\n                            <i className=\"fas fa-info-circle\"></i>\r\n                            No CV available at the moment.\r\n                          </p>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  })()}\r\n                </div>\r\n              )}\r\n\r\n              {/* Profile Overview - Hidden for Students and Free users */}\r\n              {Account.category !== \"Student\" && Account.category !== \"Free\" && (\r\n                <div className=\"section\">\r\n                  <h3>Profile Overview</h3>\r\n                  <div className=\"rating\">\r\n                    <h4>Global Rating</h4>\r\n                    <div className=\"stars\">{generateStars(Account.rate)}</div>\r\n                    <span id=\"globalRatingText\">\r\n                      {Account.rate.toFixed(1)} out of 5 ({Account.rateCount}{\" \"}\r\n                      reviews)\r\n                    </span>\r\n\r\n                    {!sameAccount && (\r\n                      <div className=\"rating-actions\">\r\n                        <button\r\n                          className=\"rating-action-button\"\r\n                          onClick={() => setOpenRatingDialog(true)}\r\n                        >\r\n                          <i className=\"fas fa-star\"></i>Rate Me\r\n                        </button>\r\n                        <button\r\n                          className=\"rating-action-button\"\r\n                          onClick={() => setOpenReserveDialog(true)}\r\n                        >\r\n                          <i className=\"fas fa-ticket-alt\"></i>Reserve Coupon\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Detailed Skills - Hidden for Students and Free users */}\r\n              {Account.category !== \"Student\" && Account.category !== \"Free\" && (\r\n                <div className=\"section\">\r\n                  <h3>Detailed Skills</h3>\r\n                  <div className=\"detailed-skills\">\r\n                    <ul>\r\n                      <li>\r\n                        <div className=\"skill-item\">\r\n                          <i className=\"fas fa-star\"></i>\r\n                          <span>Quality of Work</span>\r\n                        </div>\r\n                        <div className=\"stars\">\r\n                          {generateStars(Account.rate_Skill_QualityOfWork)}\r\n                        </div>\r\n                      </li>\r\n                      <li>\r\n                        <div className=\"skill-item\">\r\n                          <i className=\"fas fa-hand-holding-usd\"></i>\r\n                          <span>Cost Effectiveness</span>\r\n                        </div>\r\n                        <div className=\"stars\">\r\n                          {generateStars(Account.rate_Skill_CostEffectiveness)}\r\n                        </div>\r\n                      </li>\r\n                      <li>\r\n                        <div className=\"skill-item\">\r\n                          <i className=\"fas fa-hourglass-half\"></i>\r\n                          <span>Timeliness</span>\r\n                        </div>\r\n                        <div className=\"stars\">\r\n                          {generateStars(Account.rate_Skill_Timeliness)}\r\n                        </div>\r\n                      </li>\r\n                      <li>\r\n                        <div className=\"skill-item\">\r\n                          <i className=\"fas fa-comments\"></i>\r\n                          <span>Communication</span>\r\n                        </div>\r\n                        <div className=\"stars\">\r\n                          {generateStars(Account.rate_Skill_Communication)}\r\n                        </div>\r\n                      </li>\r\n                      <li>\r\n                        <div className=\"skill-item\">\r\n                          <i className=\"fas fa-rocket\"></i>\r\n                          <span>Agility</span>\r\n                        </div>\r\n                        <div className=\"stars\">\r\n                          {generateStars(Account.rate_Skill_Agility)}\r\n                        </div>\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* QR Code Section */}\r\n              <div className=\"section\">\r\n                <h3>Quick Connect</h3>\r\n                <div className=\"qr-section\">\r\n                  <div id=\"profile-qrcode\">\r\n                    <canvas\r\n                      ref={(canvas) => {\r\n                        if (canvas && Account.profile.userName) {\r\n                          // Clear any existing content\r\n                          const ctx = canvas.getContext(\"2d\");\r\n                          ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n\r\n                          import(\"qrcode\")\r\n                            .then((QRCode) => {\r\n                              QRCode.toCanvas(\r\n                                canvas,\r\n                                window.location.href,\r\n                                {\r\n                                  width: 150,\r\n                                  margin: 1,\r\n                                  color: {\r\n                                    dark: \"#2c3e50\",\r\n                                    light: \"#ffffff\",\r\n                                  },\r\n                                  errorCorrectionLevel: \"M\",\r\n                                  type: \"image/png\",\r\n                                  quality: 0.92,\r\n                                  rendererOpts: {\r\n                                    quality: 0.92,\r\n                                  },\r\n                                },\r\n                                (error) => {\r\n                                  if (error) {\r\n                                    console.error(\r\n                                      \"QR Code generation error:\",\r\n                                      error\r\n                                    );\r\n                                    return;\r\n                                  }\r\n\r\n                                  // Add logo overlay\r\n                                  const ctx = canvas.getContext(\"2d\");\r\n                                  const logo = new Image();\r\n                                  logo.crossOrigin = \"anonymous\";\r\n                                  logo.onload = () => {\r\n                                    // Calculate logo size and position (center of QR code)\r\n                                    const logoSize = 30;\r\n                                    const x = (canvas.width - logoSize) / 2;\r\n                                    const y = (canvas.height - logoSize) / 2;\r\n\r\n                                    // Draw white background circle for logo\r\n                                    ctx.fillStyle = \"#ffffff\";\r\n                                    ctx.beginPath();\r\n                                    ctx.arc(\r\n                                      x + logoSize / 2,\r\n                                      y + logoSize / 2,\r\n                                      logoSize / 2 + 2,\r\n                                      0,\r\n                                      2 * Math.PI\r\n                                    );\r\n                                    ctx.fill();\r\n\r\n                                    // Draw the logo\r\n                                    ctx.drawImage(\r\n                                      logo,\r\n                                      x,\r\n                                      y,\r\n                                      logoSize,\r\n                                      logoSize\r\n                                    );\r\n                                  };\r\n                                  logo.onerror = () => {\r\n                                    console.log(\r\n                                      \"Logo not found, QR code generated without logo\"\r\n                                    );\r\n                                  };\r\n                                  logo.src = \"/assets/idigics_logo.png\";\r\n                                }\r\n                              );\r\n                            })\r\n                            .catch((error) => {\r\n                              console.error(\r\n                                \"Failed to load QR code library:\",\r\n                                error\r\n                              );\r\n                            });\r\n                        }\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  <p className=\"qr-description\">\r\n                    Scan this QR code to quickly access my profile on any device\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Share Modal */}\r\n            {openShareModal && (\r\n              <div\r\n                className={`modal ${openShareModal ? \"is-open\" : \"\"}`}\r\n                id=\"share-modal\"\r\n              >\r\n                <div className=\"modal-content\">\r\n                  <button\r\n                    className=\"close-button\"\r\n                    onClick={() => setOpenShareModal(false)}\r\n                  >\r\n                    ×\r\n                  </button>\r\n                  <h3>Share Profile</h3>\r\n                  <div className=\"share-options\">\r\n                    <a\r\n                      href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(\r\n                        window.location.href\r\n                      )}`}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      title=\"Share on Facebook\"\r\n                    >\r\n                      <i className=\"fab fa-facebook-f\"></i>\r\n                    </a>\r\n                    <a\r\n                      href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(\r\n                        window.location.href\r\n                      )}&text=${encodeURIComponent(\r\n                        `Check out ${Account.firstName} ${Account.lastName}'s profile!`\r\n                      )}`}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      title=\"Share on Twitter\"\r\n                    >\r\n                      <i className=\"fab fa-twitter\"></i>\r\n                    </a>\r\n                  </div>\r\n                  <div id=\"qrcode\">\r\n                    <canvas\r\n                      ref={(canvas) => {\r\n                        if (canvas && openShareModal) {\r\n                          // Clear any existing content\r\n                          const ctx = canvas.getContext(\"2d\");\r\n                          ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n\r\n                          import(\"qrcode\")\r\n                            .then((QRCode) => {\r\n                              QRCode.toCanvas(\r\n                                canvas,\r\n                                window.location.href,\r\n                                {\r\n                                  width: 150,\r\n                                  margin: 1,\r\n                                  color: {\r\n                                    dark: \"#2c3e50\",\r\n                                    light: \"#ffffff\",\r\n                                  },\r\n                                  errorCorrectionLevel: \"M\",\r\n                                  type: \"image/png\",\r\n                                  quality: 0.92,\r\n                                  rendererOpts: {\r\n                                    quality: 0.92,\r\n                                  },\r\n                                },\r\n                                (error) => {\r\n                                  if (error) {\r\n                                    console.error(\r\n                                      \"QR Code generation error:\",\r\n                                      error\r\n                                    );\r\n                                    return;\r\n                                  }\r\n\r\n                                  // Add logo overlay\r\n                                  const ctx = canvas.getContext(\"2d\");\r\n                                  const logo = new Image();\r\n                                  logo.crossOrigin = \"anonymous\";\r\n                                  logo.onload = () => {\r\n                                    // Calculate logo size and position (center of QR code)\r\n                                    const logoSize = 30;\r\n                                    const x = (canvas.width - logoSize) / 2;\r\n                                    const y = (canvas.height - logoSize) / 2;\r\n\r\n                                    // Draw white background circle for logo\r\n                                    ctx.fillStyle = \"#ffffff\";\r\n                                    ctx.beginPath();\r\n                                    ctx.arc(\r\n                                      x + logoSize / 2,\r\n                                      y + logoSize / 2,\r\n                                      logoSize / 2 + 2,\r\n                                      0,\r\n                                      2 * Math.PI\r\n                                    );\r\n                                    ctx.fill();\r\n\r\n                                    // Draw the logo\r\n                                    ctx.drawImage(\r\n                                      logo,\r\n                                      x,\r\n                                      y,\r\n                                      logoSize,\r\n                                      logoSize\r\n                                    );\r\n                                  };\r\n                                  logo.onerror = () => {\r\n                                    console.log(\r\n                                      \"Logo not found, QR code generated without logo\"\r\n                                    );\r\n                                  };\r\n                                  logo.src = \"/assets/idigics_logo.png\";\r\n                                }\r\n                              );\r\n                            })\r\n                            .catch((error) => {\r\n                              console.error(\r\n                                \"Failed to load QR code library:\",\r\n                                error\r\n                              );\r\n                            });\r\n                        }\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  <button\r\n                    id=\"download-qr\"\r\n                    onClick={() => {\r\n                      import(\"qrcode\")\r\n                        .then((QRCode) => {\r\n                          // Create a temporary canvas for download\r\n                          const tempCanvas = document.createElement(\"canvas\");\r\n                          QRCode.toCanvas(\r\n                            tempCanvas,\r\n                            window.location.href,\r\n                            {\r\n                              width: 300,\r\n                              margin: 2,\r\n                              color: {\r\n                                dark: \"#2c3e50\",\r\n                                light: \"#ffffff\",\r\n                              },\r\n                              errorCorrectionLevel: \"M\",\r\n                              type: \"image/png\",\r\n                              quality: 0.92,\r\n                              rendererOpts: {\r\n                                quality: 0.92,\r\n                              },\r\n                            },\r\n                            (error) => {\r\n                              if (error) {\r\n                                console.error(\r\n                                  \"QR Code download generation error:\",\r\n                                  error\r\n                                );\r\n                                return;\r\n                              }\r\n\r\n                              // Add logo overlay to download version\r\n                              const ctx = tempCanvas.getContext(\"2d\");\r\n                              const logo = new Image();\r\n                              logo.crossOrigin = \"anonymous\";\r\n                              logo.onload = () => {\r\n                                const logoSize = 60; // Larger for download version\r\n                                const x = (tempCanvas.width - logoSize) / 2;\r\n                                const y = (tempCanvas.height - logoSize) / 2;\r\n\r\n                                // Draw white background circle for logo\r\n                                ctx.fillStyle = \"#ffffff\";\r\n                                ctx.beginPath();\r\n                                ctx.arc(\r\n                                  x + logoSize / 2,\r\n                                  y + logoSize / 2,\r\n                                  logoSize / 2 + 4,\r\n                                  0,\r\n                                  2 * Math.PI\r\n                                );\r\n                                ctx.fill();\r\n\r\n                                // Draw the logo\r\n                                ctx.drawImage(logo, x, y, logoSize, logoSize);\r\n\r\n                                // Download the canvas\r\n                                const url = tempCanvas.toDataURL(\r\n                                  \"image/png\",\r\n                                  0.92\r\n                                );\r\n                                const link = document.createElement(\"a\");\r\n                                link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;\r\n                                link.href = url;\r\n                                link.click();\r\n                              };\r\n                              logo.onerror = () => {\r\n                                // Fallback: download without logo\r\n                                const url = tempCanvas.toDataURL(\r\n                                  \"image/png\",\r\n                                  0.92\r\n                                );\r\n                                const link = document.createElement(\"a\");\r\n                                link.download = `${Account.firstName}-${Account.lastName}-profile-qr.png`;\r\n                                link.href = url;\r\n                                link.click();\r\n                              };\r\n                              logo.src = \"/assets/idigics_logo.png\";\r\n                            }\r\n                          );\r\n                        })\r\n                        .catch((error) => {\r\n                          console.error(\r\n                            \"Failed to load QR code library for download:\",\r\n                            error\r\n                          );\r\n                        });\r\n                    }}\r\n                  >\r\n                    <i className=\"fas fa-download\"></i> Download QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Contact Modal */}\r\n            {openContactModal && (\r\n              <div\r\n                className={`modal ${openContactModal ? \"is-open\" : \"\"}`}\r\n                id=\"contact-modal\"\r\n              >\r\n                <div className=\"modal-content\">\r\n                  <button\r\n                    className=\"close-button\"\r\n                    onClick={() => setOpenContactModal(false)}\r\n                  >\r\n                    ×\r\n                  </button>\r\n                  <h3>Contact Exchange</h3>\r\n                  <div className=\"contact-options\">\r\n                    <p>\r\n                      Exchange contact information with {Account.firstName}{\" \"}\r\n                      {Account.lastName}\r\n                    </p>\r\n                    <div className=\"contact-actions\">\r\n                      <button\r\n                        className=\"contact-action-button\"\r\n                        onClick={handleDownloadVCard}\r\n                      >\r\n                        <i className=\"fas fa-address-card\"></i>\r\n                        Save Contact (vCard)\r\n                      </button>\r\n                      <button\r\n                        className=\"contact-action-button\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            if (\r\n                              navigator.clipboard &&\r\n                              navigator.clipboard.writeText\r\n                            ) {\r\n                              await navigator.clipboard.writeText(\r\n                                window.location.href\r\n                              );\r\n                              toast.success(\r\n                                \"Profile link copied to clipboard!\",\r\n                                {\r\n                                  position: \"top-center\",\r\n                                  autoClose: 2000,\r\n                                }\r\n                              );\r\n                            } else {\r\n                              // Fallback for browsers that don't support clipboard API\r\n                              const textArea =\r\n                                document.createElement(\"textarea\");\r\n                              textArea.value = window.location.href;\r\n                              document.body.appendChild(textArea);\r\n                              textArea.select();\r\n                              document.execCommand(\"copy\");\r\n                              document.body.removeChild(textArea);\r\n                              toast.success(\r\n                                \"Profile link copied to clipboard!\",\r\n                                {\r\n                                  position: \"top-center\",\r\n                                  autoClose: 2000,\r\n                                }\r\n                              );\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\r\n                              \"Failed to copy to clipboard:\",\r\n                              error\r\n                            );\r\n                            toast.error(\r\n                              \"Failed to copy link. Please copy manually: \" +\r\n                                window.location.href,\r\n                              {\r\n                                position: \"top-center\",\r\n                                autoClose: 5000,\r\n                              }\r\n                            );\r\n                          }\r\n                        }}\r\n                      >\r\n                        <i className=\"fas fa-link\"></i>\r\n                        Copy Profile Link\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Rating Dialog */}\r\n            {openRatingDialog && (\r\n              <RatingDialog\r\n                onClose={() => setOpenRatingDialog(false)}\r\n                onClick={handleSendRate}\r\n                openDialog={openRatingDialog}\r\n                serialKey={SerialKey}\r\n              />\r\n            )}\r\n\r\n            {/* Reserve Dialog */}\r\n            {openReserveDialog && (\r\n              <CheckoutReserved\r\n                onApply={handleApplyCoupon}\r\n                ShowCouponSection={true}\r\n                onClose={() => setOpenReserveDialog(false)}\r\n              />\r\n            )}\r\n\r\n            {/* CV Preview Dialog */}\r\n            {openCvDialog && (\r\n              <Dialog\r\n                open={openCvDialog}\r\n                onClose={() => setOpenCvDialog(false)}\r\n                fullWidth\r\n                maxWidth=\"md\"\r\n              >\r\n                <DialogTitle>\r\n                  CV Preview <PortraitIcon />\r\n                </DialogTitle>\r\n                <DialogContent>\r\n                  <IconButton\r\n                    sx={{\r\n                      position: \"absolute\",\r\n                      right: 8,\r\n                      top: 8,\r\n                    }}\r\n                    aria-label=\"close\"\r\n                    onClick={() => setOpenCvDialog(false)}\r\n                  >\r\n                    <CloseIcon />\r\n                  </IconButton>\r\n                  <div\r\n                    style={{\r\n                      height: \"600px\",\r\n                      width: \"100%\",\r\n                      overflow: \"auto\",\r\n                    }}\r\n                  >\r\n                    <Worker\r\n                      workerUrl={`https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`}\r\n                    >\r\n                      <Viewer\r\n                        fileUrl={cvFileRef.current}\r\n                        showPreviousViewOnLoad={false}\r\n                      />\r\n                    </Worker>\r\n                  </div>\r\n                </DialogContent>\r\n              </Dialog>\r\n            )}\r\n\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// Helper functions for icons and labels\r\nconst getSocialIcon = (category) => {\r\n  switch (category?.toLowerCase()) {\r\n    case \"twitter\":\r\n      return \"fab fa-twitter\";\r\n    case \"github\":\r\n      return \"fab fa-github\";\r\n    case \"instagram\":\r\n      return \"fab fa-instagram\";\r\n    case \"facebook\":\r\n      return \"fab fa-facebook-f\";\r\n    case \"linkedin\":\r\n      return \"fab fa-linkedin-in\";\r\n    case \"youtube\":\r\n      return \"fab fa-youtube\";\r\n    case \"tiktok\":\r\n      return \"fab fa-tiktok\";\r\n    case \"snapchat\":\r\n      return \"fab fa-snapchat-ghost\";\r\n    case \"pinterest\":\r\n      return \"fab fa-pinterest\";\r\n    case \"reddit\":\r\n      return \"fab fa-reddit\";\r\n    case \"discord\":\r\n      return \"fab fa-discord\";\r\n    case \"telegram\":\r\n      return \"fab fa-telegram\";\r\n    case \"whatsapp\":\r\n      return \"fab fa-whatsapp\";\r\n    case \"website\":\r\n    case \"blog\":\r\n      return \"fas fa-globe\";\r\n    case \"portfolio\":\r\n      return \"fas fa-briefcase\";\r\n    case \"email\":\r\n      return \"fas fa-envelope\";\r\n    default:\r\n      return \"fas fa-link\";\r\n  }\r\n};\r\n\r\nconst getContactIcon = (category) => {\r\n  switch (category?.toLowerCase()) {\r\n    case \"tel\":\r\n    case \"phone\":\r\n      return \"fas fa-phone\";\r\n    case \"wa\":\r\n    case \"whatsapp\":\r\n      return \"fab fa-whatsapp\";\r\n    case \"mailto\":\r\n    case \"email\":\r\n    case \"gmail\":\r\n      return \"fas fa-envelope\";\r\n    case \"map\":\r\n    case \"location\":\r\n    case \"address\":\r\n      return \"fas fa-map-marker-alt\";\r\n    case \"sms\":\r\n      return \"fas fa-comment-sms\";\r\n    case \"fax\":\r\n      return \"fas fa-fax\";\r\n    case \"website\":\r\n      return \"fas fa-globe\";\r\n    default:\r\n      return \"fas fa-address-book\";\r\n  }\r\n};\r\n\r\nconst getContactLabel = (category, contactInfo, title) => {\r\n  if (!contactInfo || contactInfo.startsWith(\"data:application/pdf\")) {\r\n    return \"\";\r\n  }\r\n  // If we have a title, display it along with the contact info\r\n  if (title && title.trim()) {\r\n    return `${title}: ${contactInfo}`;\r\n  }\r\n  switch (category?.toLowerCase()) {\r\n    case \"tel\":\r\n    case \"phone\":\r\n    case \"phonenumber\":\r\n      return contactInfo;\r\n    case \"wa\":\r\n    case \"whatsapp\":\r\n      return contactInfo;\r\n    case \"mailto\":\r\n    case \"email\":\r\n    case \"gmail\":\r\n      return contactInfo;\r\n    case \"map\":\r\n    case \"location\":\r\n    case \"address\":\r\n      return contactInfo;\r\n    case \"sms\":\r\n      return contactInfo;\r\n    case \"fax\":\r\n      return contactInfo;\r\n    case \"website\":\r\n      return contactInfo;\r\n    default:\r\n      return contactInfo;\r\n  }\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAO,eAAe;AACtB,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,SAAS,EAAEC,aAAa,QAAQ,mBAAmB;AAC5D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,qBAAqB;AACzD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E,SACEC,GAAG,EACHC,gBAAgB,EAChBC,UAAU,EACVC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,SAASC,MAAM,EAAEC,MAAM,QAAQ,wBAAwB;AACvD,OAAO,6CAA6C;AACpD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,iBAAA,EAAAC,kBAAA;EACpB,MAAMC,MAAM,GAAG1C,MAAM,CAAC,KAAK,CAAC;EAC5B,MAAM;IAAE2C,OAAO;IAAEC;EAAa,CAAC,GAAGxC,UAAU,CAAC,CAAC;EAC9C,MAAMyC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC;IACrCqD,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZb,OAAO,EAAE;MACPc,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAI;MACjBC,MAAM,EAAE,EAAE;MACVL,EAAE,EAAE,CAAC;MACLM,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,IAAI;MACbC,mBAAmB,EAAE,EAAE;MACvBC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,CAAC;MACtBC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,IAAI,EAAE;MACJjB,EAAE,EAAE,CAAC;MACLkB,QAAQ,EAAE,EAAE;MACZL,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE;IACV,CAAC;IACDK,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,CAAC;IACZC,wBAAwB,EAAE,CAAC;IAC3BC,4BAA4B,EAAE,CAAC;IAC/BC,qBAAqB,EAAE,CAAC;IACxBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwF,SAAS,EAAEC,YAAY,CAAC,GAAGzF,QAAQ,CAAC,OAAO,CAAC;;EAEnD;EACA,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CACtD+C,SAAS,GAAG,IAAI,GAAG,KACrB,CAAC;EACD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkG,YAAY,EAAEC,eAAe,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMoG,SAAS,GAAGnG,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMoG,eAAe,GAAIC,QAAQ,IAAK;IACpCb,YAAY,CAACa,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MAAA,IAAAC,cAAA;MACF;MACA,IAAI,CAACD,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjE,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MAEA,MAAMC,QAAQ,GAAG,MAAMrG,aAAa,CAACiG,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;MAEnD,IAAIE,QAAQ,CAACC,KAAK,EAAE;QAClB,MAAM,IAAIF,KAAK,CAACC,QAAQ,CAACC,KAAK,CAAC;MACjC;;MAEA;MACA,MAAMC,cAAc,GAClB,EAAAL,cAAA,GAAAG,QAAQ,CAACG,IAAI,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,OAAO,KAAI,+BAA+B;MAC3D5G,KAAK,CAAC6G,OAAO,CAACH,cAAc,EAAE;QAC5BI,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,OAAOP,QAAQ;IACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACA,MAAMO,YAAY,GAChBP,KAAK,CAACG,OAAO,IAAI,6CAA6C;MAChE5G,KAAK,CAACyG,KAAK,CAACO,YAAY,EAAE;QACxBF,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACA,OAAO;QAAEN,KAAK,EAAEO;MAAa,CAAC;IAChC;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAOzE,OAAO,EAAE0E,aAAa,KAAK;IACxD,IAAI3E,MAAM,CAAC4E,OAAO,EAAE;IACpB,IAAI;MACF,MAAMC,QAAQ,GAAG;QACfnD,MAAM,EAAEiD,aAAa,CAAC/D,EAAE;QACxBkE,IAAI,EAAE,IAAIC,IAAI,CAAC;MACjB,CAAC;;MAED;MACA,IAAI9E,OAAO,IAAIA,OAAO,CAACW,EAAE,IAAIX,OAAO,CAACW,EAAE,KAAK+D,aAAa,CAAC/D,EAAE,EAAE;QAAA,IAAAoE,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA;QAC5DN,QAAQ,CAAC5D,MAAM,IAAA+D,qBAAA,IAAAC,gBAAA,GAAGhF,OAAO,CAACA,OAAO,cAAAgF,gBAAA,uBAAfA,gBAAA,CAAiBhE,MAAM,cAAA+D,qBAAA,cAAAA,qBAAA,GAAI,IAAI;QACjDH,QAAQ,CAACjD,OAAO,IAAAsD,qBAAA,IAAAC,iBAAA,GAAGlF,OAAO,CAACA,OAAO,cAAAkF,iBAAA,uBAAfA,iBAAA,CAAiBvD,OAAO,cAAAsD,qBAAA,cAAAA,qBAAA,GAAI,SAAS;QACxD,MAAMpH,QAAQ,CAAC+G,QAAQ,CAAC;QACxB7E,MAAM,CAAC4E,OAAO,GAAG,IAAI;MACvB;;MAEA;MACA,IAAI3E,OAAO,IAAI,IAAI,EAAE;QACnB,MAAMnC,QAAQ,CAAC+G,QAAQ,CAAC;QACxB7E,MAAM,CAAC4E,OAAO,GAAG,IAAI;MACvB;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;EACF,CAAC;EAED9G,SAAS,CAAC,MAAM;IACd,MAAMiI,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACzD,IAAIF,eAAe,EAAE;MACnBhF,YAAY,CAACgF,eAAe,CAAC;MAC7BjC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;IACA,MAAMoC,SAAS,GAAGA,CAAA,KAAM;MACtBC,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACAD,SAAS,CAAC,CAAC;;IAEX;IACA;;IAEA;IACA;IACA;EACF,CAAC,EAAE,CAACvF,OAAO,CAAC,CAAC;EAEb7C,SAAS,CAAC,MAAM;IACd,MAAMsI,OAAO,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACF;QACA,MAAMC,eAAe,GAAGpI,cAAc,CAAC,CAAC;;QAExC;QACA,IAAIoI,eAAe,KAAK,CAAC1F,OAAO,IAAI,CAACA,OAAO,CAACW,EAAE,CAAC,EAAE;UAChD,MAAMV,YAAY,CAAC,CAAC;QACtB;QAEA,MAAM0F,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;QAC5C,MAAMC,eAAe,GAAGJ,WAAW,CAACK,SAAS,CAC3CL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CACjC,CAAC;;QAED;QACA,MAAMjC,QAAQ,GAAG,MAAMzG,mBAAmB,CAACwI,eAAe,CAAC;QAC3D,MAAMrB,aAAa,GAAGV,QAAQ,CAACG,IAAI;;QAEnC;QACA,IAAIuB,eAAe,EAAE;UACnB/C,cAAc,CAAC3C,OAAO,CAACW,EAAE,KAAK+D,aAAa,CAAC/D,EAAE,CAAC;QACjD,CAAC,MAAM;UACLgC,cAAc,CAAC,KAAK,CAAC;QACvB;;QAEA;QACA,MAAM8B,eAAe,CAACiB,eAAe,GAAG1F,OAAO,GAAG,IAAI,EAAE0E,aAAa,CAAC;MACxE,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdkB,OAAO,CAAClB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C;IACF,CAAC;IAEDwB,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACzF,OAAO,CAAC,CAAC;EAEb,MAAMwF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMG,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,eAAe,GAAGJ,WAAW,CAACK,SAAS,CAC3CL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CACjC,CAAC;IACD,IAAI;MACF3F,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM0D,QAAQ,GAAG,MAAMzG,mBAAmB,CAACwI,eAAe,CAAC;MAC3D,IAAI/B,QAAQ,CAACC,KAAK,EAAE;QAClB,MAAM,IAAIF,KAAK,CAACC,QAAQ,CAACC,KAAK,CAAC;MACjC;MAEAzD,UAAU,CAACwD,QAAQ,CAACG,IAAI,CAAC;MACzB5B,cAAc,CAACyB,QAAQ,CAACG,IAAI,CAACnE,OAAO,CAACuB,WAAW,CAAC;MACjDkB,cAAc,CAACuB,QAAQ,CAACG,IAAI,CAACnE,OAAO,CAACe,WAAW,IAAI,EAAE,CAAC;MACvDT,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACd3D,YAAY,CAAC,KAAK,CAAC;MACnBJ,QAAQ,CAAC,MAAM,CAAC;IAClB;EACF,CAAC;EAED,MAAMgG,cAAc,GAAG,MAAO/B,IAAI,IAAK;IACrC,IAAI;MACF,MAAMH,QAAQ,GAAG,MAAMtG,SAAS,CAACyG,IAAI,CAAC;MACtC,IAAIH,QAAQ,CAACC,KAAK,EAAE,MAAM,IAAIF,KAAK,CAACC,QAAQ,CAACC,KAAK,CAAC;MAEnDoB,YAAY,CAACc,UAAU,CAAC,WAAW,CAAC;MACpC/F,YAAY,CAAC,IAAI,CAAC;MAClB5C,KAAK,CAAC6G,OAAO,CAAC,wBAAwB,EAAE;QACtCC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,KAAK,EAAE;MACdzG,KAAK,CAACyG,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;QACzBE,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACAtE,YAAY,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMmG,aAAa,GAAG,SAAAA,CAACC,KAAK,EAAmB;IAAA,IAAjBC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACxC,IAAIG,SAAS,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,KAAK,CAAC;IACnC,MAAMS,iBAAiB,GAAG,GAAG;IAC7B,MAAMC,QAAQ,GAAGV,KAAK,GAAG,CAAC,IAAIS,iBAAiB;IAC/C,MAAME,UAAU,GAAGV,QAAQ,GAAGK,SAAS,IAAII,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAE5D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,SAAS,EAAEM,CAAC,EAAE,EAAE;MAClCP,SAAS,CAACQ,IAAI,eAACpI,OAAA;QAAqBqI,SAAS,EAAC;MAAkB,GAAzC,QAAQF,CAAC,EAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAAkC,CAAC,CAAC;IACxE;IACA,IAAIT,QAAQ,EAAE;MACZL,SAAS,CAACQ,IAAI,eACZpI,OAAA;QAAcqI,SAAS,EAAC;MAA8B,GAA/C,MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAA6C,CAC5D,CAAC;IACH;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,EAAEC,CAAC,EAAE,EAAE;MACnCP,SAAS,CAACQ,IAAI,eAACpI,OAAA;QAAsBqI,SAAS,EAAC;MAAoB,GAA5C,SAASF,CAAC,EAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,GAAAC,KAAoC,CAAC,CAAC;IAC3E;IAEA,OAAOd,SAAS;EAClB,CAAC;EAED,MAAMe,eAAe,GAAG,MAAAA,CAAOC,MAAM,EAAEC,OAAO,EAAE9G,QAAQ,KAAK;IAC3D;IACA,IAAI,CAAC+G,WAAW,CAACD,OAAO,CAAC,EAAE;MACzBzH,QAAQ,CAAC,MAAM,CAAC;MAChB;IACF;;IAEA;IACA,IAAI2H,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,mBAAmB,CAAC,EAAE;MAClD;MACA,IAAI,CAACrF,WAAW,EAAE;QAAA,IAAAsF,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA;QAChBrK,SAAS,CAAC;UACR4J,MAAM,EAAEA,MAAM;UACdjG,MAAM,EAAElB,OAAO,CAACI,EAAE;UAClBK,MAAM,GAAAgH,qBAAA,IAAAC,gBAAA,GAAE1H,OAAO,CAACP,OAAO,cAAAiI,gBAAA,uBAAfA,gBAAA,CAAiBjH,MAAM,cAAAgH,qBAAA,cAAAA,qBAAA,GAAI,IAAI;UACvCrG,OAAO,GAAAuG,qBAAA,IAAAC,iBAAA,GAAE5H,OAAO,CAACP,OAAO,cAAAmI,iBAAA,uBAAfA,iBAAA,CAAiBxG,OAAO,cAAAuG,qBAAA,cAAAA,qBAAA,GAAI,IAAI;UACzCrH,QAAQ,EAAEA,QAAQ;UAClBgE,IAAI,EAAE,IAAIC,IAAI,CAAC;QACjB,CAAC,CAAC,CAACsD,KAAK,CAACjD,OAAO,CAAClB,KAAK,CAAC,CAAC,CAAC;MAC3B;MACA2B,MAAM,CAACyC,IAAI,CAACV,OAAO,EAAE,QAAQ,CAAC;IAChC,CAAC,MAAM;MACL;MACAW,UAAU,CAAC,YAAY;QACrB,IAAI,CAAC5F,WAAW,EAAE;UAAA,IAAA6F,sBAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,iBAAA;UAChB,MAAM5K,SAAS,CAAC;YACd4J,MAAM,EAAEA,MAAM;YACdjG,MAAM,EAAElB,OAAO,CAACI,EAAE;YAClBK,MAAM,GAAAuH,sBAAA,IAAAC,iBAAA,GAAEjI,OAAO,CAACP,OAAO,cAAAwI,iBAAA,uBAAfA,iBAAA,CAAiBxH,MAAM,cAAAuH,sBAAA,cAAAA,sBAAA,GAAI,IAAI;YACvC5G,OAAO,GAAA8G,sBAAA,IAAAC,iBAAA,GAAEnI,OAAO,CAACP,OAAO,cAAA0I,iBAAA,uBAAfA,iBAAA,CAAiB/G,OAAO,cAAA8G,sBAAA,cAAAA,sBAAA,GAAI,IAAI;YACzC5H,QAAQ,EAAEA,QAAQ;YAClBgE,IAAI,EAAE,IAAIC,IAAI,CAAC;UACjB,CAAC,CAAC;QACJ;QACAc,MAAM,CAACyC,IAAI,CAACV,OAAO,EAAE,QAAQ,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAED,MAAMC,WAAW,GAAIe,GAAG,IAAK;IAC3B,IAAI;MACF,IAAIC,GAAG,CAACD,GAAG,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC1C,IAAIC,IAAI,GAAG,GAAG;IACd,MAAMC,YAAY,GAAGC,kBAAkB,CAACH,KAAK,CAAC;IAC9C,MAAMI,QAAQ,GAAGJ,KAAK,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAC9C,MAAMC,OAAO,GAAGN,KAAK,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;;IAE5C;IACAf,UAAU,CAAC,MAAM;MACf,QAAQS,IAAI,CAACQ,WAAW,CAAC,CAAC;QACxB,KAAK,KAAK;QACV,KAAK,OAAO;QACZ,KAAK,aAAa;UAChBN,IAAI,GAAG,OAAOG,QAAQ,EAAE;UACxB;QACF,KAAK,IAAI;QACT,KAAK,UAAU;UACbH,IAAI,GAAG,iBAAiBK,OAAO,EAAE;UACjC;UACA,IAAIzB,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YAClDnC,MAAM,CAACC,QAAQ,CAACoD,IAAI,GAAGA,IAAI;UAC7B,CAAC,MAAM;YACLrD,MAAM,CAACyC,IAAI,CAACY,IAAI,EAAE,QAAQ,CAAC;UAC7B;UACA;QACF,KAAK,QAAQ;QACb,KAAK,OAAO;UACVA,IAAI,GAAG,UAAUD,KAAK,EAAE;UACxB;QACF,KAAK,OAAO;UACVC,IAAI,GAAG,iDAAiDE,kBAAkB,CACxEH,KACF,CAAC,EAAE;UACH;UACA,IAAInB,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YAClDnC,MAAM,CAACC,QAAQ,CAACoD,IAAI,GAAGA,IAAI;UAC7B,CAAC,MAAM;YACLrD,MAAM,CAACyC,IAAI,CAACY,IAAI,EAAE,QAAQ,CAAC;UAC7B;UACA;QACF,KAAK,KAAK;UACRA,IAAI,GAAG,8BAA8BC,YAAY,EAAE;UACnD;UACA,IAAIrB,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,mBAAmB,CAAC,EAAE;YAClDnC,MAAM,CAACC,QAAQ,CAACoD,IAAI,GAAGA,IAAI;UAC7B,CAAC,MAAM;YACLrD,MAAM,CAACyC,IAAI,CAACY,IAAI,EAAE,QAAQ,CAAC;UAC7B;UACA;QACF,KAAK,KAAK;UACRA,IAAI,GAAG,OAAOG,QAAQ,EAAE;UACxB;QACF;UACEH,IAAI,GAAG,GAAG;UACV;MACJ;MAEA,IAAIA,IAAI,KAAK,GAAG,EAAE;QAChBrD,MAAM,CAACC,QAAQ,CAACoD,IAAI,GAAGA,IAAI;MAC7B;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;EAED,MAAMO,aAAa,GACjB,EAAArK,iBAAA,GAAAoB,OAAO,CAACuB,QAAQ,cAAA3C,iBAAA,uBAAhBA,iBAAA,CAAkBsK,MAAM,CACrBC,OAAO;IAAA,IAAAC,oBAAA;IAAA,OACND,OAAO,CAACE,QAAQ,IAChB,GAAAD,oBAAA,GAACD,OAAO,CAACG,WAAW,cAAAF,oBAAA,eAAnBA,oBAAA,CAAqBG,UAAU,CAAC,sBAAsB,CAAC;EAAA,CAC5D,CAAC,KAAI,EAAE;EAET,MAAMC,gBAAgB,GAAGP,aAAa,CAAChD,MAAM,GAAG,CAAC;EAEjD,MAAMwD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MAAA,IAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA;MACFjF,OAAO,CAACkF,GAAG,CAAC,yBAAyB,CAAC;MAEtC,IAAIC,KAAK,GAAG,gCAAgC;MAC5CA,KAAK,IAAI,MAAM/J,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,MAAM;MAC1D0J,KAAK,IAAI,KAAK/J,OAAO,CAACK,QAAQ,IAAIL,OAAO,CAACG,SAAS,SAAS;MAE5D,KAAAuJ,iBAAA,GAAI1J,OAAO,CAACP,OAAO,cAAAiK,iBAAA,eAAfA,iBAAA,CAAiB/I,UAAU,EAAE;QAC/BoJ,KAAK,IAAI,SAAS/J,OAAO,CAACP,OAAO,CAACkB,UAAU,MAAM;MACpD;;MAEA;MACA,KAAAgJ,iBAAA,GAAI3J,OAAO,CAACP,OAAO,cAAAkK,iBAAA,eAAfA,iBAAA,CAAiBK,YAAY,EAAE;QACjCD,KAAK,IAAI,OAAO/J,OAAO,CAACP,OAAO,CAACuK,YAAY,MAAM;MACpD;;MAEA;MACA,KAAAJ,iBAAA,GAAI5J,OAAO,CAACP,OAAO,cAAAmK,iBAAA,eAAfA,iBAAA,CAAiB9I,cAAc,EAAE;QACnC,IAAI;UACF8D,OAAO,CAACkF,GAAG,CAAC,wBAAwB,EAAE9J,OAAO,CAACP,OAAO,CAACqB,cAAc,CAAC;UACrE,MAAM2C,QAAQ,GAAG,MAAMwG,KAAK,CAACjK,OAAO,CAACP,OAAO,CAACqB,cAAc,CAAC;UAC5D,IAAI2C,QAAQ,CAACyG,EAAE,EAAE;YAAA,IAAAC,mBAAA;YACf,MAAMC,IAAI,GAAG,MAAM3G,QAAQ,CAAC2G,IAAI,CAAC,CAAC;YAClC,MAAMC,WAAW,GAAGD,IAAI,CAAC5B,IAAI;YAC7B,MAAM8B,SAAS,IAAAH,mBAAA,GAAGE,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAAJ,mBAAA,uBAAzBA,mBAAA,CAA2BK,WAAW,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;cACrD,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;cAC/BD,MAAM,CAACE,MAAM,GAAG,UAAUtC,CAAC,EAAE;gBAC3B,MAAMuC,UAAU,GAAGvC,CAAC,CAACwC,MAAM,CAACC,MAAM,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChDR,KAAK,IAAI,8BAA8BO,SAAS,IAAIO,UAAU,MAAM;cACtE,CAAC;cACDH,MAAM,CAACM,aAAa,CAACZ,IAAI,CAAC;YAC5B;UACF;QACF,CAAC,CAAC,OAAO1G,KAAK,EAAE;UACdkB,OAAO,CAACqG,IAAI,CAAC,4CAA4C,EAAEvH,KAAK,CAAC;QACnE;MACF;;MAEA;MACA,CAAAmG,kBAAA,GAAA7J,OAAO,CAACuB,QAAQ,cAAAsI,kBAAA,uBAAhBA,kBAAA,CAAkBqB,OAAO,CAAE/B,OAAO,IAAK;QAAA,IAAAgC,qBAAA,EAAAC,iBAAA;QACrC,MAAMC,UAAU,IAAAF,qBAAA,GAAGhC,OAAO,CAACG,WAAW,cAAA6B,qBAAA,uBAAnBA,qBAAA,CAAqB5H,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC8H,UAAU,IAAIA,UAAU,CAAC9B,UAAU,CAAC,sBAAsB,CAAC,EAC9D;QAEF,MAAMV,QAAQ,GAAGwC,UAAU,CAACvC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QACnD,MAAMC,OAAO,GAAGsC,UAAU,CAACvC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;QAEjD,SAAAsC,iBAAA,GAAQjC,OAAO,CAAC7I,QAAQ,cAAA8K,iBAAA,uBAAhBA,iBAAA,CAAkBpC,WAAW,CAAC,CAAC;UACrC,KAAK,KAAK;UACV,KAAK,OAAO;UACZ,KAAK,aAAa;YAChBe,KAAK,IAAI,uBAAuBlB,QAAQ,MAAM;YAC9C;UACF,KAAK,QAAQ;UACb,KAAK,OAAO;UACZ,KAAK,OAAO;YACVkB,KAAK,IAAI,mBAAmBsB,UAAU,MAAM;YAC5C;UACF,KAAK,KAAK;UACV,KAAK,UAAU;UACf,KAAK,SAAS;YACZtB,KAAK,IAAI,mBAAmBsB,UAAU,CAACvC,OAAO,CAC5C,KAAK,EACL,KACF,CAAC,UAAU;YACXiB,KAAK,IAAI,2CAA2CnB,kBAAkB,CACpEyC,UACF,CAAC,MAAM;YACP;UACF,KAAK,IAAI;UACT,KAAK,UAAU;YACbtB,KAAK,IAAI,mCAAmChB,OAAO,MAAM;YACzD;UACF,KAAK,KAAK;YACRgB,KAAK,IAAI,qBAAqBlB,QAAQ,MAAM;YAC5C;UACF,KAAK,SAAS;YACZkB,KAAK,IAAI,OAAOsB,UAAU,MAAM;YAChC;QACJ;MACF,CAAC,CAAC;;MAEF;MACAtJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmJ,OAAO,CAAEI,IAAI,IAAK;QAAA,IAAAC,aAAA;QAC7B,KAAAA,aAAA,GAAID,IAAI,CAAClE,OAAO,cAAAmE,aAAA,eAAZA,aAAA,CAAchI,IAAI,CAAC,CAAC,EAAE;UACxBwG,KAAK,IAAI,OAAOuB,IAAI,CAAClE,OAAO,CAAC7D,IAAI,CAAC,CAAC,MAAM;QAC3C;MACF,CAAC,CAAC;;MAEF;MACAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiJ,OAAO,CAAEI,IAAI,IAAK;QAAA,IAAAE,cAAA;QAC7B,KAAAA,cAAA,GAAIF,IAAI,CAAClE,OAAO,cAAAoE,cAAA,eAAZA,cAAA,CAAcjI,IAAI,CAAC,CAAC,EAAE;UACxBwG,KAAK,IAAI,OAAOuB,IAAI,CAAClE,OAAO,CAAC7D,IAAI,CAAC,CAAC,MAAM;QAC3C;MACF,CAAC,CAAC;;MAEF;MACAwG,KAAK,IAAI,OAAO1E,MAAM,CAACC,QAAQ,CAACoD,IAAI,MAAM;;MAE1C;MACA,MAAM+C,IAAI,GAAG,yDAAyDzL,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,EAAE;MAC7G0J,KAAK,IAAI,QAAQ0B,IAAI,MAAM;;MAE3B;MACA1B,KAAK,IAAI,OAAO,IAAIxF,IAAI,CAAC,CAAC,CAACmH,WAAW,CAAC,CAAC,CAAC5C,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO;MACrEiB,KAAK,IAAI,WAAW;MAEpBnF,OAAO,CAACkF,GAAG,CAAC,oBAAoB,EAAEC,KAAK,CAAC;;MAExC;MACA,MAAMK,IAAI,GAAG,IAAIuB,IAAI,CAAC,CAAC5B,KAAK,CAAC,EAAE;QAAEvB,IAAI,EAAE;MAA2B,CAAC,CAAC;MACpE,MAAMJ,GAAG,GAAGC,GAAG,CAACuD,eAAe,CAACxB,IAAI,CAAC;MACrC,MAAMyB,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MAC/CF,WAAW,CAACnD,IAAI,GAAGN,GAAG;MAEtB,MAAM4D,YAAY,GAAG,GAAGhM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,UAAU,CACpE2I,WAAW,CAAC,CAAC,CACbF,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;MAC/B+C,WAAW,CAACI,QAAQ,GAAG,GAAGD,YAAY,MAAM;MAE5CF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,WAAW,CAAC;MACtCA,WAAW,CAACO,KAAK,CAAC,CAAC;MACnBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,WAAW,CAAC;MACtCxD,GAAG,CAACiE,eAAe,CAAClE,GAAG,CAAC;MAExBxD,OAAO,CAACkF,GAAG,CAAC,2BAA2B,CAAC;MAExC7M,KAAK,CAAC6G,OAAO,CAAC,uCAAuC,EAAE;QACrDC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,KAAK,EAAE;MACdkB,OAAO,CAAClB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzG,KAAK,CAACyG,KAAK,CAAC,iCAAiC,EAAE;QAC7CK,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEzF,OAAA,CAAAE,SAAA;IAAA8N,QAAA,gBACEhO,OAAA,CAACF,MAAM;MAAAkO,QAAA,gBACLhO,OAAA;QAAAgO,QAAA,EAAQ,GAAGvM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ;MAAoB;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC7EzI,OAAA;QACEiO,IAAI,EAAC,aAAa;QAClBC,OAAO,EAAE,gBAAgBzM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,GAC5D,CAAAxB,iBAAA,GAAAmB,OAAO,CAACP,OAAO,cAAAZ,iBAAA,eAAfA,iBAAA,CAAiB8B,UAAU,GAAG,KAAKX,OAAO,CAACP,OAAO,CAACkB,UAAU,EAAE,GAAG,EAAE;MAChB;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGFzI,OAAA;QACEmO,QAAQ,EAAC,UAAU;QACnBD,OAAO,EAAE,GAAGzM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ;MAAqB;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACFzI,OAAA;QACEmO,QAAQ,EAAC,gBAAgB;QACzBD,OAAO,EAAE,gBAAgBzM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,GAC5D,CAAAvB,iBAAA,GAAAkB,OAAO,CAACP,OAAO,cAAAX,iBAAA,eAAfA,iBAAA,CAAiB6B,UAAU,GAAG,KAAKX,OAAO,CAACP,OAAO,CAACkB,UAAU,EAAE,GAAG,EAAE;MAChB;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACFzI,OAAA;QAAMmO,QAAQ,EAAC,SAAS;QAACD,OAAO,EAAC;MAAS;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CzI,OAAA;QAAMmO,QAAQ,EAAC,QAAQ;QAACD,OAAO,EAAEpH,MAAM,CAACC,QAAQ,CAACoD;MAAK;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxD,EAAAjI,kBAAA,GAAAiB,OAAO,CAACP,OAAO,cAAAV,kBAAA,uBAAfA,kBAAA,CAAiB+B,cAAc,kBAC9BvC,OAAA;QAAMmO,QAAQ,EAAC,UAAU;QAACD,OAAO,EAAEzM,OAAO,CAACP,OAAO,CAACqB;MAAe;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACrE,eACDzI,OAAA;QAAMmO,QAAQ,EAAC,cAAc;QAACD,OAAO,EAAC;MAAS;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlDzI,OAAA;QAAMiO,IAAI,EAAC,cAAc;QAACC,OAAO,EAAC;MAAqB;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DzI,OAAA;QACEiO,IAAI,EAAC,eAAe;QACpBC,OAAO,EAAE,GAAGzM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ;MAAqB;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACFzI,OAAA;QACEiO,IAAI,EAAC,qBAAqB;QAC1BC,OAAO,EAAE,gBAAgBzM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,GAC5D,CAAArB,kBAAA,GAAAgB,OAAO,CAACP,OAAO,cAAAT,kBAAA,eAAfA,kBAAA,CAAiB2B,UAAU,GAAG,KAAKX,OAAO,CAACP,OAAO,CAACkB,UAAU,EAAE,GAAG,EAAE;MAChB;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EACD,EAAA/H,kBAAA,GAAAe,OAAO,CAACP,OAAO,cAAAR,kBAAA,uBAAfA,kBAAA,CAAiB6B,cAAc,kBAC9BvC,OAAA;QAAMiO,IAAI,EAAC,eAAe;QAACC,OAAO,EAAEzM,OAAO,CAACP,OAAO,CAACqB;MAAe;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACtE,eAGDzI,OAAA;QAAMmO,QAAQ,EAAC,oBAAoB;QAACD,OAAO,EAAEzM,OAAO,CAACG;MAAU;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClEzI,OAAA;QAAMmO,QAAQ,EAAC,mBAAmB;QAACD,OAAO,EAAEzM,OAAO,CAACK;MAAS;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC/D,EAAA9H,kBAAA,GAAAc,OAAO,CAACP,OAAO,cAAAP,kBAAA,uBAAfA,kBAAA,CAAiBiC,QAAQ,kBACxB5C,OAAA;QACEmO,QAAQ,EAAC,kBAAkB;QAC3BD,OAAO,EAAEzM,OAAO,CAACP,OAAO,CAAC0B;MAAS;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAETzI,OAAA;MACEqI,SAAS,EAAE,qBACT,CAAAzH,kBAAA,GAAAa,OAAO,CAACP,OAAO,cAAAN,kBAAA,eAAfA,kBAAA,CAAiB0B,mBAAmB,GAAG,WAAW,GAAG,EAAE,EACtD;MACH8L,KAAK,EAAE;QACLC,eAAe,EAAE,CAAAxN,kBAAA,GAAAY,OAAO,CAACP,OAAO,cAAAL,kBAAA,eAAfA,kBAAA,CAAiByB,mBAAmB,GACjD,OAAOb,OAAO,CAACP,OAAO,CAACoB,mBAAmB,GAAG,GAC7C;MACN,CAAE;MAAA0L,QAAA,EAEDzM,SAAS,gBACRvB,OAAA,CAACb,GAAG;QACFmP,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,OAAO;UAClBC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE;QACP,CAAE;QAAAZ,QAAA,gBAEFhO,OAAA,CAACZ,gBAAgB;UAACyP,IAAI,EAAE;QAAG;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BzI,OAAA,CAACX,UAAU;UAACyP,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,eAAe;UAAAf,QAAA,EAAC;QAE/C;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENzI,OAAA;QAAKqI,SAAS,EAAC,mBAAmB;QAAA2F,QAAA,gBAEhChO,OAAA;UAAKqI,SAAS,EAAC,gBAAgB;UAAA2F,QAAA,gBAC7BhO,OAAA;YACEgP,GAAG,EACDvN,OAAO,CAACP,OAAO,CAACqB,cAAc,IAC9B,sDACE,EAAAzB,kBAAA,GAAAW,OAAO,CAACG,SAAS,cAAAd,kBAAA,uBAAjBA,kBAAA,CAAmBmO,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GACjC,EAAAlO,iBAAA,GAAAU,OAAO,CAACK,QAAQ,cAAAf,iBAAA,uBAAhBA,iBAAA,CAAkBkO,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EACrC;YACDC,GAAG,EAAC,SAAS;YACb7G,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFzI,OAAA;YAAIqI,SAAS,EAAC,WAAW;YAAA2F,QAAA,GACtBvM,OAAO,CAACG,SAAS,EAAC,GAAC,EAACH,OAAO,CAACK,QAAQ;UAAA;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACLzI,OAAA;YAAGqI,SAAS,EAAC,YAAY;YAAA2F,QAAA,EAAEvM,OAAO,CAACP,OAAO,CAACkB;UAAU;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG1DzI,OAAA;YACEqI,SAAS,EAAC,yBAAyB;YACnC8G,OAAO,EAAEA,CAAA,KAAMhL,mBAAmB,CAAC,IAAI,CAAE;YACzC,cAAW,kBAAkB;YAC7BiL,KAAK,EAAC,kBAAkB;YAAApB,QAAA,eAExBhO,OAAA;cAAGqI,SAAS,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAGTzI,OAAA;YACEqI,SAAS,EAAC,cAAc;YACxB8G,OAAO,EAAEA,CAAA,KAAMlL,iBAAiB,CAAC,IAAI,CAAE;YACvC,cAAW,eAAe;YAAA+J,QAAA,eAE1BhO,OAAA;cAAGqI,SAAS,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzI,OAAA;UAAKqI,SAAS,EAAC,aAAa;UAAA2F,QAAA,gBAC1BhO,OAAA;YACEqI,SAAS,EAAE,cACTvE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EACpC;YACHqL,OAAO,EAAEA,CAAA,KAAMxK,eAAe,CAAC,OAAO,CAAE;YAAAqJ,QAAA,gBAExChO,OAAA;cAAGqI,SAAS,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,SACjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzI,OAAA;YACEqI,SAAS,EAAE,cACTvE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EACpC;YACHqL,OAAO,EAAEA,CAAA,KAAMxK,eAAe,CAAC,OAAO,CAAE;YAAAqJ,QAAA,gBAExChO,OAAA;cAAGqI,SAAS,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,SACjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzI,OAAA;UACEqI,SAAS,EAAE,eAAevE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAkK,QAAA,gBAElEhO,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAe;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBzI,OAAA;cAAIqI,SAAS,EAAC,YAAY;cAAA2F,QAAA,EACvBxK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmH,MAAM,CACjBoC,IAAI,IACHA,IAAI,CAAChL,QAAQ,IACb,CAAC,CACC,KAAK,EACL,OAAO,EACP,aAAa,EACb,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,OAAO,EACP,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS,CACV,CAACmK,QAAQ,CAACa,IAAI,CAAChL,QAAQ,CAAC0I,WAAW,CAAC,CAAC,CAC1C,CAAC,CAAC4E,GAAG,CAAEtC,IAAI,iBACT/M,OAAA;gBAAkBqI,SAAS,EAAC,WAAW;gBAAA2F,QAAA,eACrChO,OAAA;kBACEmK,IAAI,EAAC,GAAG;kBACRgF,OAAO,EAAGpF,CAAC,IAAK;oBACdA,CAAC,CAACuF,cAAc,CAAC,CAAC;oBAClB3G,eAAe,CAACoE,IAAI,CAAClL,EAAE,EAAEkL,IAAI,CAAClE,OAAO,EAAEkE,IAAI,CAAChL,QAAQ,CAAC;kBACvD,CAAE;kBAAAiM,QAAA,gBAEFhO,OAAA;oBAAGqI,SAAS,EAAEkH,aAAa,CAACxC,IAAI,CAAChL,QAAQ;kBAAE;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC/CsE,IAAI,CAACqC,KAAK;gBAAA;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAVGsE,IAAI,CAAClL,EAAE;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGL/E,WAAW,IAAIA,WAAW,CAACgE,MAAM,GAAG,CAAC,iBACpC1H,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAQ;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBzI,OAAA;cAAIqI,SAAS,EAAC,YAAY;cAAA2F,QAAA,EACvBtK,WAAW,CAAC2L,GAAG,CAAEtC,IAAI,iBACpB/M,OAAA;gBAAkBqI,SAAS,EAAC,WAAW;gBAAA2F,QAAA,eACrChO,OAAA;kBACEmK,IAAI,EAAC,GAAG;kBACRgF,OAAO,EAAGpF,CAAC,IAAK;oBACdA,CAAC,CAACuF,cAAc,CAAC,CAAC;oBAClB3G,eAAe,CAACoE,IAAI,CAAClL,EAAE,EAAEkL,IAAI,CAAClE,OAAO,EAAE,QAAQ,CAAC;kBAClD,CAAE;kBAAAmF,QAAA,GAEDjB,IAAI,CAACyC,IAAI,gBACRxP,OAAA;oBACEgP,GAAG,EAAEjC,IAAI,CAACyC,IAAK;oBACfN,GAAG,EAAEnC,IAAI,CAACqC,KAAM;oBAChBhB,KAAK,EAAE;sBACLqB,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdC,WAAW,EAAE,MAAM;sBACnBC,YAAY,EAAE;oBAChB;kBAAE;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFzI,OAAA;oBAAGqI,SAAS,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAC/B,EACAsE,IAAI,CAACqC,KAAK;gBAAA;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAvBGsE,IAAI,CAAClL,EAAE;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACN,EACAwC,gBAAgB,iBACfjL,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAY;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBzI,OAAA;cAAIqI,SAAS,EAAC,YAAY;cAAA2F,QAAA,GAAAhN,kBAAA,GACvBS,OAAO,CAACuB,QAAQ,cAAAhC,kBAAA,uBAAhBA,kBAAA,CACG2J,MAAM,CACLC,OAAO;gBAAA,IAAAiF,qBAAA;gBAAA,OACNjF,OAAO,CAACE,QAAQ,IAChB,GAAA+E,qBAAA,GAACjF,OAAO,CAACG,WAAW,cAAA8E,qBAAA,eAAnBA,qBAAA,CAAqB7E,UAAU,CAC9B,sBACF,CAAC;cAAA,CACL,CAAC,CACAqE,GAAG,CAAEzE,OAAO,iBACX5K,OAAA;gBAAqBqI,SAAS,EAAC,WAAW;gBAAA2F,QAAA,eACxChO,OAAA;kBACEqI,SAAS,EAAC,gBAAgB;kBAC1B8G,OAAO,EAAEA,CAAA,KAAM;oBACbnF,kBAAkB,CAChBY,OAAO,CAAC7I,QAAQ,EAChB6I,OAAO,CAACG,WACV,CAAC;kBACH,CAAE;kBAAAiD,QAAA,gBAEFhO,OAAA;oBAAGqI,SAAS,EAAEyH,cAAc,CAAClF,OAAO,CAAC7I,QAAQ;kBAAE;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnDsH,eAAe,CACdnF,OAAO,CAAC7I,QAAQ,EAChB6I,OAAO,CAACG,WAAW,EACnBH,OAAO,CAACwE,KACV,CAAC;gBAAA;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC,GAhBFmC,OAAO,CAAC/I,EAAE;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBf,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACN,eAEDzI,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,eACtBhO,OAAA;cAAQqI,SAAS,EAAC,cAAc;cAAC8G,OAAO,EAAEjE,mBAAoB;cAAA8C,QAAA,gBAC5DhO,OAAA;gBAAGqI,SAAS,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,wBAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzI,OAAA;UACEqI,SAAS,EAAE,eAAevE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAkK,QAAA,gBAElEhO,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,eACtBhO,OAAA;cAAKqI,SAAS,EAAC,eAAe;cAAA2F,QAAA,gBAC5BhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAAgO,QAAA,EAAQ;gBAAa;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAAC,GAAG,EACjChH,OAAO,CAACP,OAAO,CAACkB,UAAU,IAAI,eAAe;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACHhH,OAAO,CAACP,OAAO,CAAC2B,OAAO,iBACtB7C,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAAgO,QAAA,EAAQ;gBAAS;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChH,OAAO,CAACP,OAAO,CAAC2B,OAAO;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CACJ,EACAhH,OAAO,CAACM,QAAQ,iBACf/B,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAAgO,QAAA,EAAQ;gBAAS;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChH,OAAO,CAACM,QAAQ;cAAA;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CACJ,eACDzI,OAAA;gBAAAgO,QAAA,GAAG,6BAC0B,EAACvM,OAAO,CAACG,SAAS,EAAE,GAAG,EACjDH,OAAO,CAACK,QAAQ,EAAC,GAClB,EAACL,OAAO,CAACP,OAAO,CAACkB,UAAU,IACzB,iBAAiBX,OAAO,CAACP,OAAO,CAACkB,UAAU,EAAE,EAC9CX,OAAO,CAACP,OAAO,CAAC2B,OAAO,IACtB,aAAapB,OAAO,CAACP,OAAO,CAAC2B,OAAO,EAAE,EAAC,kFAG3C;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLhH,OAAO,CAACM,QAAQ,KAAK,SAAS,iBAC7B/B,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAgB;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACxB,CAACuH,kBAAA,IAAM;cACN,MAAMC,SAAS,IAAAD,kBAAA,GAAGvO,OAAO,CAACuB,QAAQ,cAAAgN,kBAAA,uBAAhBA,kBAAA,CAAkBE,IAAI,CACrCtF,OAAO,IACNA,OAAO,CAAC7I,QAAQ,KAAK,QAAQ,IAAI6I,OAAO,CAACE,QAC7C,CAAC;cAED,IAAImF,SAAS,IAAIA,SAAS,CAAClF,WAAW,EAAE;gBACtC,oBACE/K,OAAA;kBAAKqI,SAAS,EAAC,YAAY;kBAAA2F,QAAA,eACzBhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAKqI,SAAS,EAAC,WAAW;sBAAA2F,QAAA,gBACxBhO,OAAA;wBAAGqI,SAAS,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3CzI,OAAA;wBAAKqI,SAAS,EAAC,SAAS;wBAAA2F,QAAA,gBACtBhO,OAAA;0BAAAgO,QAAA,EAAKiC,SAAS,CAACb,KAAK,IAAI;wBAAc;0BAAA9G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC5CzI,OAAA;0BAAAgO,QAAA,EAAG;wBAAgC;0BAAA1F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNzI,OAAA;sBAAKqI,SAAS,EAAC,YAAY;sBAAA2F,QAAA,gBACzBhO,OAAA;wBACEqI,SAAS,EAAC,6BAA6B;wBACvC8G,OAAO,EAAEA,CAAA,KAAM;0BACbzK,SAAS,CAACmB,OAAO,GAAGoK,SAAS,CAAClF,WAAW;0BACzCtG,eAAe,CAAC,IAAI,CAAC;wBACvB,CAAE;wBAAAuJ,QAAA,gBAEFhO,OAAA;0BAAGqI,SAAS,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,cAEhC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTzI,OAAA;wBACEqI,SAAS,EAAC,8BAA8B;wBACxC8G,OAAO,EAAEA,CAAA,KAAM;0BACb,MAAMpC,IAAI,GAAGQ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;0BACxCT,IAAI,CAAC5C,IAAI,GAAG8F,SAAS,CAAClF,WAAW;0BACjCgC,IAAI,CAACW,QAAQ,GAAG,GAAGjM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,SAAS;0BACjEiL,IAAI,CAACR,MAAM,GAAG,QAAQ;0BACtBgB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACb,IAAI,CAAC;0BAC/BA,IAAI,CAACc,KAAK,CAAC,CAAC;0BACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACf,IAAI,CAAC;wBACjC,CAAE;wBAAAiB,QAAA,gBAEFhO,OAAA;0BAAGqI,SAAS,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAErC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV,CAAC,MAAM;gBACL,oBACEzI,OAAA;kBAAKqI,SAAS,EAAC,YAAY;kBAAA2F,QAAA,eACzBhO,OAAA;oBAAGqI,SAAS,EAAC,eAAe;oBAAA2F,QAAA,gBAC1BhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,kCAExC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAEV;YACF,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN,EAGAhH,OAAO,CAACM,QAAQ,KAAK,SAAS,IAAIN,OAAO,CAACM,QAAQ,KAAK,MAAM,iBAC5D/B,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAgB;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzI,OAAA;cAAKqI,SAAS,EAAC,QAAQ;cAAA2F,QAAA,gBACrBhO,OAAA;gBAAAgO,QAAA,EAAI;cAAa;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBzI,OAAA;gBAAKqI,SAAS,EAAC,OAAO;gBAAA2F,QAAA,EAAE1G,aAAa,CAAC7F,OAAO,CAACwB,IAAI;cAAC;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DzI,OAAA;gBAAM6B,EAAE,EAAC,kBAAkB;gBAAAmM,QAAA,GACxBvM,OAAO,CAACwB,IAAI,CAACkN,OAAO,CAAC,CAAC,CAAC,EAAC,aAAW,EAAC1O,OAAO,CAACyB,SAAS,EAAE,GAAG,EAAC,UAE9D;cAAA;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAEN,CAAC7E,WAAW,iBACX5D,OAAA;gBAAKqI,SAAS,EAAC,gBAAgB;gBAAA2F,QAAA,gBAC7BhO,OAAA;kBACEqI,SAAS,EAAC,sBAAsB;kBAChC8G,OAAO,EAAEA,CAAA,KAAM9K,mBAAmB,CAAC,IAAI,CAAE;kBAAA2J,QAAA,gBAEzChO,OAAA;oBAAGqI,SAAS,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WACjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzI,OAAA;kBACEqI,SAAS,EAAC,sBAAsB;kBAChC8G,OAAO,EAAEA,CAAA,KAAM5K,oBAAoB,CAAC,IAAI,CAAE;kBAAAyJ,QAAA,gBAE1ChO,OAAA;oBAAGqI,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,kBACvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAhH,OAAO,CAACM,QAAQ,KAAK,SAAS,IAAIN,OAAO,CAACM,QAAQ,KAAK,MAAM,iBAC5D/B,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAe;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBzI,OAAA;cAAKqI,SAAS,EAAC,iBAAiB;cAAA2F,QAAA,eAC9BhO,OAAA;gBAAAgO,QAAA,gBACEhO,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/BzI,OAAA;sBAAAgO,QAAA,EAAM;oBAAe;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACNzI,OAAA;oBAAKqI,SAAS,EAAC,OAAO;oBAAA2F,QAAA,EACnB1G,aAAa,CAAC7F,OAAO,CAAC0B,wBAAwB;kBAAC;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLzI,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3CzI,OAAA;sBAAAgO,QAAA,EAAM;oBAAkB;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNzI,OAAA;oBAAKqI,SAAS,EAAC,OAAO;oBAAA2F,QAAA,EACnB1G,aAAa,CAAC7F,OAAO,CAAC2B,4BAA4B;kBAAC;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLzI,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzCzI,OAAA;sBAAAgO,QAAA,EAAM;oBAAU;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACNzI,OAAA;oBAAKqI,SAAS,EAAC,OAAO;oBAAA2F,QAAA,EACnB1G,aAAa,CAAC7F,OAAO,CAAC4B,qBAAqB;kBAAC;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLzI,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnCzI,OAAA;sBAAAgO,QAAA,EAAM;oBAAa;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACNzI,OAAA;oBAAKqI,SAAS,EAAC,OAAO;oBAAA2F,QAAA,EACnB1G,aAAa,CAAC7F,OAAO,CAAC6B,wBAAwB;kBAAC;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLzI,OAAA;kBAAAgO,QAAA,gBACEhO,OAAA;oBAAKqI,SAAS,EAAC,YAAY;oBAAA2F,QAAA,gBACzBhO,OAAA;sBAAGqI,SAAS,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjCzI,OAAA;sBAAAgO,QAAA,EAAM;oBAAO;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACNzI,OAAA;oBAAKqI,SAAS,EAAC,OAAO;oBAAA2F,QAAA,EACnB1G,aAAa,CAAC7F,OAAO,CAAC8B,kBAAkB;kBAAC;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDzI,OAAA;YAAKqI,SAAS,EAAC,SAAS;YAAA2F,QAAA,gBACtBhO,OAAA;cAAAgO,QAAA,EAAI;YAAa;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBzI,OAAA;cAAKqI,SAAS,EAAC,YAAY;cAAA2F,QAAA,gBACzBhO,OAAA;gBAAK6B,EAAE,EAAC,gBAAgB;gBAAAmM,QAAA,eACtBhO,OAAA;kBACEoQ,GAAG,EAAGC,MAAM,IAAK;oBACf,IAAIA,MAAM,IAAI5O,OAAO,CAACP,OAAO,CAAC0B,QAAQ,EAAE;sBACtC;sBACA,MAAM0N,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;sBACnCD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAACZ,KAAK,EAAEY,MAAM,CAACX,MAAM,CAAC;sBAEhD,MAAM,CAAC,QAAQ,CAAC,CACbe,IAAI,CAAEC,MAAM,IAAK;wBAChBA,MAAM,CAACC,QAAQ,CACbN,MAAM,EACNvJ,MAAM,CAACC,QAAQ,CAACoD,IAAI,EACpB;0BACEsF,KAAK,EAAE,GAAG;0BACVmB,MAAM,EAAE,CAAC;0BACT7B,KAAK,EAAE;4BACL8B,IAAI,EAAE,SAAS;4BACfC,KAAK,EAAE;0BACT,CAAC;0BACDC,oBAAoB,EAAE,GAAG;0BACzB9G,IAAI,EAAE,WAAW;0BACjB+G,OAAO,EAAE,IAAI;0BACbC,YAAY,EAAE;4BACZD,OAAO,EAAE;0BACX;wBACF,CAAC,EACA7L,KAAK,IAAK;0BACT,IAAIA,KAAK,EAAE;4BACTkB,OAAO,CAAClB,KAAK,CACX,2BAA2B,EAC3BA,KACF,CAAC;4BACD;0BACF;;0BAEA;0BACA,MAAMmL,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;0BACnC,MAAMW,IAAI,GAAG,IAAIC,KAAK,CAAC,CAAC;0BACxBD,IAAI,CAACE,WAAW,GAAG,WAAW;0BAC9BF,IAAI,CAAC7E,MAAM,GAAG,MAAM;4BAClB;4BACA,MAAMgF,QAAQ,GAAG,EAAE;4BACnB,MAAMC,CAAC,GAAG,CAACjB,MAAM,CAACZ,KAAK,GAAG4B,QAAQ,IAAI,CAAC;4BACvC,MAAME,CAAC,GAAG,CAAClB,MAAM,CAACX,MAAM,GAAG2B,QAAQ,IAAI,CAAC;;4BAExC;4BACAf,GAAG,CAACkB,SAAS,GAAG,SAAS;4BACzBlB,GAAG,CAACmB,SAAS,CAAC,CAAC;4BACfnB,GAAG,CAACoB,GAAG,CACLJ,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAChBE,CAAC,GAAGF,QAAQ,GAAG,CAAC,EAChBA,QAAQ,GAAG,CAAC,GAAG,CAAC,EAChB,CAAC,EACD,CAAC,GAAGvJ,IAAI,CAAC6J,EACX,CAAC;4BACDrB,GAAG,CAACsB,IAAI,CAAC,CAAC;;4BAEV;4BACAtB,GAAG,CAACuB,SAAS,CACXX,IAAI,EACJI,CAAC,EACDC,CAAC,EACDF,QAAQ,EACRA,QACF,CAAC;0BACH,CAAC;0BACDH,IAAI,CAACY,OAAO,GAAG,MAAM;4BACnBzL,OAAO,CAACkF,GAAG,CACT,gDACF,CAAC;0BACH,CAAC;0BACD2F,IAAI,CAAClC,GAAG,GAAG,0BAA0B;wBACvC,CACF,CAAC;sBACH,CAAC,CAAC,CACD1F,KAAK,CAAEnE,KAAK,IAAK;wBAChBkB,OAAO,CAAClB,KAAK,CACX,iCAAiC,EACjCA,KACF,CAAC;sBACH,CAAC,CAAC;oBACN;kBACF;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzI,OAAA;gBAAGqI,SAAS,EAAC,gBAAgB;gBAAA2F,QAAA,EAAC;cAE9B;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLzE,cAAc,iBACbhE,OAAA;UACEqI,SAAS,EAAE,SAASrE,cAAc,GAAG,SAAS,GAAG,EAAE,EAAG;UACtDnC,EAAE,EAAC,aAAa;UAAAmM,QAAA,eAEhBhO,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAA2F,QAAA,gBAC5BhO,OAAA;cACEqI,SAAS,EAAC,cAAc;cACxB8G,OAAO,EAAEA,CAAA,KAAMlL,iBAAiB,CAAC,KAAK,CAAE;cAAA+J,QAAA,EACzC;YAED;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzI,OAAA;cAAAgO,QAAA,EAAI;YAAa;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBzI,OAAA;cAAKqI,SAAS,EAAC,eAAe;cAAA2F,QAAA,gBAC5BhO,OAAA;gBACEmK,IAAI,EAAE,gDAAgDE,kBAAkB,CACtEvD,MAAM,CAACC,QAAQ,CAACoD,IAClB,CAAC,EAAG;gBACJoC,MAAM,EAAC,QAAQ;gBACfwF,GAAG,EAAC,qBAAqB;gBACzB3C,KAAK,EAAC,mBAAmB;gBAAApB,QAAA,eAEzBhO,OAAA;kBAAGqI,SAAS,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACJzI,OAAA;gBACEmK,IAAI,EAAE,wCAAwCE,kBAAkB,CAC9DvD,MAAM,CAACC,QAAQ,CAACoD,IAClB,CAAC,SAASE,kBAAkB,CAC1B,aAAa5I,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,aACpD,CAAC,EAAG;gBACJyK,MAAM,EAAC,QAAQ;gBACfwF,GAAG,EAAC,qBAAqB;gBACzB3C,KAAK,EAAC,kBAAkB;gBAAApB,QAAA,eAExBhO,OAAA;kBAAGqI,SAAS,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzI,OAAA;cAAK6B,EAAE,EAAC,QAAQ;cAAAmM,QAAA,eACdhO,OAAA;gBACEoQ,GAAG,EAAGC,MAAM,IAAK;kBACf,IAAIA,MAAM,IAAIrM,cAAc,EAAE;oBAC5B;oBACA,MAAMsM,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;oBACnCD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,MAAM,CAACZ,KAAK,EAAEY,MAAM,CAACX,MAAM,CAAC;oBAEhD,MAAM,CAAC,QAAQ,CAAC,CACbe,IAAI,CAAEC,MAAM,IAAK;sBAChBA,MAAM,CAACC,QAAQ,CACbN,MAAM,EACNvJ,MAAM,CAACC,QAAQ,CAACoD,IAAI,EACpB;wBACEsF,KAAK,EAAE,GAAG;wBACVmB,MAAM,EAAE,CAAC;wBACT7B,KAAK,EAAE;0BACL8B,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE;wBACT,CAAC;wBACDC,oBAAoB,EAAE,GAAG;wBACzB9G,IAAI,EAAE,WAAW;wBACjB+G,OAAO,EAAE,IAAI;wBACbC,YAAY,EAAE;0BACZD,OAAO,EAAE;wBACX;sBACF,CAAC,EACA7L,KAAK,IAAK;wBACT,IAAIA,KAAK,EAAE;0BACTkB,OAAO,CAAClB,KAAK,CACX,2BAA2B,EAC3BA,KACF,CAAC;0BACD;wBACF;;wBAEA;wBACA,MAAMmL,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;wBACnC,MAAMW,IAAI,GAAG,IAAIC,KAAK,CAAC,CAAC;wBACxBD,IAAI,CAACE,WAAW,GAAG,WAAW;wBAC9BF,IAAI,CAAC7E,MAAM,GAAG,MAAM;0BAClB;0BACA,MAAMgF,QAAQ,GAAG,EAAE;0BACnB,MAAMC,CAAC,GAAG,CAACjB,MAAM,CAACZ,KAAK,GAAG4B,QAAQ,IAAI,CAAC;0BACvC,MAAME,CAAC,GAAG,CAAClB,MAAM,CAACX,MAAM,GAAG2B,QAAQ,IAAI,CAAC;;0BAExC;0BACAf,GAAG,CAACkB,SAAS,GAAG,SAAS;0BACzBlB,GAAG,CAACmB,SAAS,CAAC,CAAC;0BACfnB,GAAG,CAACoB,GAAG,CACLJ,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAChBE,CAAC,GAAGF,QAAQ,GAAG,CAAC,EAChBA,QAAQ,GAAG,CAAC,GAAG,CAAC,EAChB,CAAC,EACD,CAAC,GAAGvJ,IAAI,CAAC6J,EACX,CAAC;0BACDrB,GAAG,CAACsB,IAAI,CAAC,CAAC;;0BAEV;0BACAtB,GAAG,CAACuB,SAAS,CACXX,IAAI,EACJI,CAAC,EACDC,CAAC,EACDF,QAAQ,EACRA,QACF,CAAC;wBACH,CAAC;wBACDH,IAAI,CAACY,OAAO,GAAG,MAAM;0BACnBzL,OAAO,CAACkF,GAAG,CACT,gDACF,CAAC;wBACH,CAAC;wBACD2F,IAAI,CAAClC,GAAG,GAAG,0BAA0B;sBACvC,CACF,CAAC;oBACH,CAAC,CAAC,CACD1F,KAAK,CAAEnE,KAAK,IAAK;sBAChBkB,OAAO,CAAClB,KAAK,CACX,iCAAiC,EACjCA,KACF,CAAC;oBACH,CAAC,CAAC;kBACN;gBACF;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzI,OAAA;cACE6B,EAAE,EAAC,aAAa;cAChBsN,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAM,CAAC,QAAQ,CAAC,CACbsB,IAAI,CAAEC,MAAM,IAAK;kBAChB;kBACA,MAAMsB,UAAU,GAAGzE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;kBACnDkD,MAAM,CAACC,QAAQ,CACbqB,UAAU,EACVlL,MAAM,CAACC,QAAQ,CAACoD,IAAI,EACpB;oBACEsF,KAAK,EAAE,GAAG;oBACVmB,MAAM,EAAE,CAAC;oBACT7B,KAAK,EAAE;sBACL8B,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE;oBACT,CAAC;oBACDC,oBAAoB,EAAE,GAAG;oBACzB9G,IAAI,EAAE,WAAW;oBACjB+G,OAAO,EAAE,IAAI;oBACbC,YAAY,EAAE;sBACZD,OAAO,EAAE;oBACX;kBACF,CAAC,EACA7L,KAAK,IAAK;oBACT,IAAIA,KAAK,EAAE;sBACTkB,OAAO,CAAClB,KAAK,CACX,oCAAoC,EACpCA,KACF,CAAC;sBACD;oBACF;;oBAEA;oBACA,MAAMmL,GAAG,GAAG0B,UAAU,CAACzB,UAAU,CAAC,IAAI,CAAC;oBACvC,MAAMW,IAAI,GAAG,IAAIC,KAAK,CAAC,CAAC;oBACxBD,IAAI,CAACE,WAAW,GAAG,WAAW;oBAC9BF,IAAI,CAAC7E,MAAM,GAAG,MAAM;sBAClB,MAAMgF,QAAQ,GAAG,EAAE,CAAC,CAAC;sBACrB,MAAMC,CAAC,GAAG,CAACU,UAAU,CAACvC,KAAK,GAAG4B,QAAQ,IAAI,CAAC;sBAC3C,MAAME,CAAC,GAAG,CAACS,UAAU,CAACtC,MAAM,GAAG2B,QAAQ,IAAI,CAAC;;sBAE5C;sBACAf,GAAG,CAACkB,SAAS,GAAG,SAAS;sBACzBlB,GAAG,CAACmB,SAAS,CAAC,CAAC;sBACfnB,GAAG,CAACoB,GAAG,CACLJ,CAAC,GAAGD,QAAQ,GAAG,CAAC,EAChBE,CAAC,GAAGF,QAAQ,GAAG,CAAC,EAChBA,QAAQ,GAAG,CAAC,GAAG,CAAC,EAChB,CAAC,EACD,CAAC,GAAGvJ,IAAI,CAAC6J,EACX,CAAC;sBACDrB,GAAG,CAACsB,IAAI,CAAC,CAAC;;sBAEV;sBACAtB,GAAG,CAACuB,SAAS,CAACX,IAAI,EAAEI,CAAC,EAAEC,CAAC,EAAEF,QAAQ,EAAEA,QAAQ,CAAC;;sBAE7C;sBACA,MAAMxH,GAAG,GAAGmI,UAAU,CAACC,SAAS,CAC9B,WAAW,EACX,IACF,CAAC;sBACD,MAAMlF,IAAI,GAAGQ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;sBACxCT,IAAI,CAACW,QAAQ,GAAG,GAAGjM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,iBAAiB;sBACzEiL,IAAI,CAAC5C,IAAI,GAAGN,GAAG;sBACfkD,IAAI,CAACc,KAAK,CAAC,CAAC;oBACd,CAAC;oBACDqD,IAAI,CAACY,OAAO,GAAG,MAAM;sBACnB;sBACA,MAAMjI,GAAG,GAAGmI,UAAU,CAACC,SAAS,CAC9B,WAAW,EACX,IACF,CAAC;sBACD,MAAMlF,IAAI,GAAGQ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;sBACxCT,IAAI,CAACW,QAAQ,GAAG,GAAGjM,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACK,QAAQ,iBAAiB;sBACzEiL,IAAI,CAAC5C,IAAI,GAAGN,GAAG;sBACfkD,IAAI,CAACc,KAAK,CAAC,CAAC;oBACd,CAAC;oBACDqD,IAAI,CAAClC,GAAG,GAAG,0BAA0B;kBACvC,CACF,CAAC;gBACH,CAAC,CAAC,CACD1F,KAAK,CAAEnE,KAAK,IAAK;kBAChBkB,OAAO,CAAClB,KAAK,CACX,8CAA8C,EAC9CA,KACF,CAAC;gBACH,CAAC,CAAC;cACN,CAAE;cAAA6I,QAAA,gBAEFhO,OAAA;gBAAGqI,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,gBACrC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAvE,gBAAgB,iBACflE,OAAA;UACEqI,SAAS,EAAE,SAASnE,gBAAgB,GAAG,SAAS,GAAG,EAAE,EAAG;UACxDrC,EAAE,EAAC,eAAe;UAAAmM,QAAA,eAElBhO,OAAA;YAAKqI,SAAS,EAAC,eAAe;YAAA2F,QAAA,gBAC5BhO,OAAA;cACEqI,SAAS,EAAC,cAAc;cACxB8G,OAAO,EAAEA,CAAA,KAAMhL,mBAAmB,CAAC,KAAK,CAAE;cAAA6J,QAAA,EAC3C;YAED;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzI,OAAA;cAAAgO,QAAA,EAAI;YAAgB;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBzI,OAAA;cAAKqI,SAAS,EAAC,iBAAiB;cAAA2F,QAAA,gBAC9BhO,OAAA;gBAAAgO,QAAA,GAAG,oCACiC,EAACvM,OAAO,CAACG,SAAS,EAAE,GAAG,EACxDH,OAAO,CAACK,QAAQ;cAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACJzI,OAAA;gBAAKqI,SAAS,EAAC,iBAAiB;gBAAA2F,QAAA,gBAC9BhO,OAAA;kBACEqI,SAAS,EAAC,uBAAuB;kBACjC8G,OAAO,EAAEjE,mBAAoB;kBAAA8C,QAAA,gBAE7BhO,OAAA;oBAAGqI,SAAS,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,wBAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzI,OAAA;kBACEqI,SAAS,EAAC,uBAAuB;kBACjC8G,OAAO,EAAE,MAAAA,CAAA,KAAY;oBACnB,IAAI;sBACF,IACEpG,SAAS,CAACmJ,SAAS,IACnBnJ,SAAS,CAACmJ,SAAS,CAACC,SAAS,EAC7B;wBACA,MAAMpJ,SAAS,CAACmJ,SAAS,CAACC,SAAS,CACjCrL,MAAM,CAACC,QAAQ,CAACoD,IAClB,CAAC;wBACDzL,KAAK,CAAC6G,OAAO,CACX,mCAAmC,EACnC;0BACEC,QAAQ,EAAE,YAAY;0BACtBC,SAAS,EAAE;wBACb,CACF,CAAC;sBACH,CAAC,MAAM;wBACL;wBACA,MAAM2M,QAAQ,GACZ7E,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;wBACpC4E,QAAQ,CAAClI,KAAK,GAAGpD,MAAM,CAACC,QAAQ,CAACoD,IAAI;wBACrCoD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACwE,QAAQ,CAAC;wBACnCA,QAAQ,CAACC,MAAM,CAAC,CAAC;wBACjB9E,QAAQ,CAAC+E,WAAW,CAAC,MAAM,CAAC;wBAC5B/E,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACsE,QAAQ,CAAC;wBACnC1T,KAAK,CAAC6G,OAAO,CACX,mCAAmC,EACnC;0BACEC,QAAQ,EAAE,YAAY;0BACtBC,SAAS,EAAE;wBACb,CACF,CAAC;sBACH;oBACF,CAAC,CAAC,OAAON,KAAK,EAAE;sBACdkB,OAAO,CAAClB,KAAK,CACX,8BAA8B,EAC9BA,KACF,CAAC;sBACDzG,KAAK,CAACyG,KAAK,CACT,6CAA6C,GAC3C2B,MAAM,CAACC,QAAQ,CAACoD,IAAI,EACtB;wBACE3E,QAAQ,EAAE,YAAY;wBACtBC,SAAS,EAAE;sBACb,CACF,CAAC;oBACH;kBACF,CAAE;kBAAAuI,QAAA,gBAEFhO,OAAA;oBAAGqI,SAAS,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,qBAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGArE,gBAAgB,iBACfpE,OAAA,CAACf,YAAY;UACXsT,OAAO,EAAEA,CAAA,KAAMlO,mBAAmB,CAAC,KAAK,CAAE;UAC1C8K,OAAO,EAAE/H,cAAe;UACxBoL,UAAU,EAAEpO,gBAAiB;UAC7BqO,SAAS,EAAEpR;QAAU;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACF,EAGAnE,iBAAiB,iBAChBtE,OAAA,CAACd,gBAAgB;UACfwT,OAAO,EAAE7N,iBAAkB;UAC3B8N,iBAAiB,EAAE,IAAK;UACxBJ,OAAO,EAAEA,CAAA,KAAMhO,oBAAoB,CAAC,KAAK;QAAE;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACF,EAGAjE,YAAY,iBACXxE,OAAA,CAACV,MAAM;UACLiK,IAAI,EAAE/E,YAAa;UACnB+N,OAAO,EAAEA,CAAA,KAAM9N,eAAe,CAAC,KAAK,CAAE;UACtCmO,SAAS;UACTC,QAAQ,EAAC,IAAI;UAAA7E,QAAA,gBAEbhO,OAAA,CAACR,WAAW;YAAAwO,QAAA,GAAC,aACA,eAAAhO,OAAA,CAACH,YAAY;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACdzI,OAAA,CAACT,aAAa;YAAAyO,QAAA,gBACZhO,OAAA,CAACP,UAAU;cACT6O,EAAE,EAAE;gBACF9I,QAAQ,EAAE,UAAU;gBACpBsN,KAAK,EAAE,CAAC;gBACRC,GAAG,EAAE;cACP,CAAE;cACF,cAAW,OAAO;cAClB5D,OAAO,EAAEA,CAAA,KAAM1K,eAAe,CAAC,KAAK,CAAE;cAAAuJ,QAAA,eAEtChO,OAAA,CAACJ,SAAS;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbzI,OAAA;cACEoO,KAAK,EAAE;gBACLsB,MAAM,EAAE,OAAO;gBACfD,KAAK,EAAE,MAAM;gBACbuD,QAAQ,EAAE;cACZ,CAAE;cAAAhF,QAAA,eAEFhO,OAAA,CAACN,MAAM;gBACLuT,SAAS,EAAE,+DAAgE;gBAAAjF,QAAA,eAE3EhO,OAAA,CAACL,MAAM;kBACLuT,OAAO,EAAExO,SAAS,CAACmB,OAAQ;kBAC3BsN,sBAAsB,EAAE;gBAAM;kBAAA7K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAArI,EAAA,CAx5CMD,OAAO;EAAA,QAEuBxB,UAAU,EAC3BG,WAAW;AAAA;AAAAsU,EAAA,GAHxBjT,OAAO;AAy5Cb,MAAMoP,aAAa,GAAIxN,QAAQ,IAAK;EAClC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0I,WAAW,CAAC,CAAC;IAC7B,KAAK,SAAS;MACZ,OAAO,gBAAgB;IACzB,KAAK,QAAQ;MACX,OAAO,eAAe;IACxB,KAAK,WAAW;MACd,OAAO,kBAAkB;IAC3B,KAAK,UAAU;MACb,OAAO,mBAAmB;IAC5B,KAAK,UAAU;MACb,OAAO,oBAAoB;IAC7B,KAAK,SAAS;MACZ,OAAO,gBAAgB;IACzB,KAAK,QAAQ;MACX,OAAO,eAAe;IACxB,KAAK,UAAU;MACb,OAAO,uBAAuB;IAChC,KAAK,WAAW;MACd,OAAO,kBAAkB;IAC3B,KAAK,QAAQ;MACX,OAAO,eAAe;IACxB,KAAK,SAAS;MACZ,OAAO,gBAAgB;IACzB,KAAK,UAAU;MACb,OAAO,iBAAiB;IAC1B,KAAK,UAAU;MACb,OAAO,iBAAiB;IAC1B,KAAK,SAAS;IACd,KAAK,MAAM;MACT,OAAO,cAAc;IACvB,KAAK,WAAW;MACd,OAAO,kBAAkB;IAC3B,KAAK,OAAO;MACV,OAAO,iBAAiB;IAC1B;MACE,OAAO,aAAa;EACxB;AACF,CAAC;AAED,MAAMqF,cAAc,GAAI/N,QAAQ,IAAK;EACnC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0I,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;IACV,KAAK,OAAO;MACV,OAAO,cAAc;IACvB,KAAK,IAAI;IACT,KAAK,UAAU;MACb,OAAO,iBAAiB;IAC1B,KAAK,QAAQ;IACb,KAAK,OAAO;IACZ,KAAK,OAAO;MACV,OAAO,iBAAiB;IAC1B,KAAK,KAAK;IACV,KAAK,UAAU;IACf,KAAK,SAAS;MACZ,OAAO,uBAAuB;IAChC,KAAK,KAAK;MACR,OAAO,oBAAoB;IAC7B,KAAK,KAAK;MACR,OAAO,YAAY;IACrB,KAAK,SAAS;MACZ,OAAO,cAAc;IACvB;MACE,OAAO,qBAAqB;EAChC;AACF,CAAC;AAED,MAAMsF,eAAe,GAAGA,CAAChO,QAAQ,EAAEgJ,WAAW,EAAEqE,KAAK,KAAK;EACxD,IAAI,CAACrE,WAAW,IAAIA,WAAW,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;IAClE,OAAO,EAAE;EACX;EACA;EACA,IAAIoE,KAAK,IAAIA,KAAK,CAACpK,IAAI,CAAC,CAAC,EAAE;IACzB,OAAO,GAAGoK,KAAK,KAAKrE,WAAW,EAAE;EACnC;EACA,QAAQhJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0I,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;IACV,KAAK,OAAO;IACZ,KAAK,aAAa;MAChB,OAAOM,WAAW;IACpB,KAAK,IAAI;IACT,KAAK,UAAU;MACb,OAAOA,WAAW;IACpB,KAAK,QAAQ;IACb,KAAK,OAAO;IACZ,KAAK,OAAO;MACV,OAAOA,WAAW;IACpB,KAAK,KAAK;IACV,KAAK,UAAU;IACf,KAAK,SAAS;MACZ,OAAOA,WAAW;IACpB,KAAK,KAAK;MACR,OAAOA,WAAW;IACpB,KAAK,KAAK;MACR,OAAOA,WAAW;IACpB,KAAK,SAAS;MACZ,OAAOA,WAAW;IACpB;MACE,OAAOA,WAAW;EACtB;AACF,CAAC;AAED,eAAe5K,OAAO;AAAC,IAAAiT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}