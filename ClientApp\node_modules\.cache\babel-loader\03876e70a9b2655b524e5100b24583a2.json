{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\GoalTrackerWidget.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, LinearProgress, IconButton, Collapse, List, ListItem, ListItemIcon, ListItemText, Chip, Paper } from '@mui/material';\nimport { Flag as FlagIcon, ExpandMore as ExpandIcon, ExpandLess as CollapseIcon, CheckCircle as CheckIcon, RadioButtonUnchecked as UncheckedIcon, TrendingUp as TrendingIcon } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { UXStorageManager } from './UXCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FloatingWidget = styled(Paper)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'fixed',\n    bottom: theme.spacing(3),\n    right: theme.spacing(3),\n    width: 300,\n    borderRadius: theme.spacing(2),\n    background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.05) 100%)',\n    border: '1px solid rgba(76, 175, 80, 0.3)',\n    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n    backdropFilter: 'blur(10px)',\n    zIndex: 998,\n    [theme.breakpoints.down('md')]: {\n      right: theme.spacing(2),\n      width: 280\n    }\n  };\n});\n_c = FloatingWidget;\nconst HeaderBox = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    padding: theme.spacing(2),\n    borderBottom: `1px solid ${theme.palette.divider}`,\n    cursor: 'pointer',\n    '&:hover': {\n      backgroundColor: 'rgba(0,0,0,0.02)'\n    }\n  };\n});\n_c2 = HeaderBox;\nconst ContentBox = styled(Box)(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    padding: theme.spacing(0, 2, 2, 2)\n  };\n});\n_c3 = ContentBox;\nconst GoalTrackerWidget = () => {\n  _s();\n  const [expanded, setExpanded] = useState(false);\n  const [goals, setGoals] = useState([]);\n  const [completedGoals, setCompletedGoals] = useState([]);\n  const defaultGoals = [{\n    id: 'complete_profile',\n    title: 'Complete Your Profile',\n    description: 'Add photo, bio, and contact info',\n    category: 'Profile',\n    points: 20,\n    completed: false\n  }, {\n    id: 'add_social_links',\n    title: 'Connect Social Media',\n    description: 'Add at least 3 social media links',\n    category: 'Links',\n    points: 15,\n    completed: false\n  }, {\n    id: 'customize_appearance',\n    title: 'Customize Appearance',\n    description: 'Choose theme and layout options',\n    category: 'Design',\n    points: 10,\n    completed: false\n  }, {\n    id: 'share_profile',\n    title: 'Share Your Profile',\n    description: 'Share your profile link 5 times',\n    category: 'Growth',\n    points: 25,\n    completed: false\n  }, {\n    id: 'check_analytics',\n    title: 'Review Analytics',\n    description: 'Check your profile analytics',\n    category: 'Insights',\n    points: 15,\n    completed: false\n  }];\n  useEffect(() => {\n    // Load goals from localStorage or use defaults\n    const savedGoals = localStorage.getItem('user_goals');\n    const savedCompleted = localStorage.getItem('completed_goals');\n    if (savedGoals) {\n      setGoals(JSON.parse(savedGoals));\n    } else {\n      setGoals(defaultGoals);\n      localStorage.setItem('user_goals', JSON.stringify(defaultGoals));\n    }\n    if (savedCompleted) {\n      setCompletedGoals(JSON.parse(savedCompleted));\n    }\n  }, []);\n  const toggleGoal = goalId => {\n    const updatedGoals = goals.map(goal => goal.id === goalId ? {\n      ...goal,\n      completed: !goal.completed\n    } : goal);\n    setGoals(updatedGoals);\n    localStorage.setItem('user_goals', JSON.stringify(updatedGoals));\n\n    // Update completed goals for points calculation\n    const completed = updatedGoals.filter(goal => goal.completed);\n    setCompletedGoals(completed);\n    localStorage.setItem('completed_goals', JSON.stringify(completed));\n  };\n  const totalPoints = goals.reduce((sum, goal) => sum + goal.points, 0);\n  const earnedPoints = completedGoals.reduce((sum, goal) => sum + goal.points, 0);\n  const progress = totalPoints > 0 ? earnedPoints / totalPoints * 100 : 0;\n  const getCategoryColor = category => {\n    const colors = {\n      'Profile': '#2196F3',\n      'Links': '#4CAF50',\n      'Design': '#FF9800',\n      'Growth': '#9C27B0',\n      'Insights': '#F44336'\n    };\n    return colors[category] || '#757575';\n  };\n  return /*#__PURE__*/_jsxDEV(FloatingWidget, {\n    elevation: 8,\n    children: [/*#__PURE__*/_jsxDEV(HeaderBox, {\n      onClick: () => setExpanded(!expanded),\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(FlagIcon, {\n            sx: {\n              color: 'success.main',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: \"bold\",\n            children: \"Goal Tracker\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: `${earnedPoints}/${totalPoints} pts`,\n            size: \"small\",\n            color: \"success\",\n            variant: \"outlined\",\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), expanded ? /*#__PURE__*/_jsxDEV(CollapseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this) : /*#__PURE__*/_jsxDEV(ExpandIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: progress,\n          sx: {\n            height: 6,\n            borderRadius: 3,\n            backgroundColor: 'rgba(76, 175, 80, 0.2)',\n            '& .MuiLinearProgress-bar': {\n              backgroundColor: 'success.main'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          mt: 0.5,\n          children: [Math.round(progress), \"% Complete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: expanded,\n      children: /*#__PURE__*/_jsxDEV(ContentBox, {\n        children: [/*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          children: goals.map(goal => /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            onClick: () => toggleGoal(goal.id),\n            sx: {\n              borderRadius: 1,\n              mb: 0.5,\n              '&:hover': {\n                backgroundColor: 'rgba(0,0,0,0.04)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              sx: {\n                minWidth: 32\n              },\n              children: goal.completed ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(UncheckedIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    textDecoration: goal.completed ? 'line-through' : 'none',\n                    color: goal.completed ? 'text.secondary' : 'text.primary'\n                  },\n                  children: goal.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${goal.points}pts`,\n                  size: \"small\",\n                  sx: {\n                    backgroundColor: getCategoryColor(goal.category),\n                    color: 'white',\n                    fontSize: '0.7rem',\n                    height: 20\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this),\n              secondary: goal.description,\n              secondaryTypographyProps: {\n                variant: 'caption',\n                color: goal.completed ? 'text.disabled' : 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, goal.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), progress === 100 && /*#__PURE__*/_jsxDEV(Box, {\n          mt: 2,\n          p: 2,\n          sx: {\n            backgroundColor: 'rgba(76, 175, 80, 0.1)',\n            borderRadius: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TrendingIcon, {\n            color: \"success\",\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"success.main\",\n            fontWeight: \"bold\",\n            children: \"\\uD83C\\uDF89 All Goals Complete!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"You're making great progress!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(GoalTrackerWidget, \"po2NVS9OnJVs4az51kdUpWPK730=\");\n_c4 = GoalTrackerWidget;\nexport default GoalTrackerWidget;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FloatingWidget\");\n$RefreshReg$(_c2, \"HeaderBox\");\n$RefreshReg$(_c3, \"ContentBox\");\n$RefreshReg$(_c4, \"GoalTrackerWidget\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "LinearProgress", "IconButton", "Collapse", "List", "ListItem", "ListItemIcon", "ListItemText", "Chip", "Paper", "Flag", "FlagIcon", "ExpandMore", "ExpandIcon", "ExpandLess", "CollapseIcon", "CheckCircle", "CheckIcon", "RadioButton<PERSON><PERSON><PERSON>ed", "UncheckedIcon", "TrendingUp", "TrendingIcon", "styled", "UXStorageManager", "jsxDEV", "_jsxDEV", "FloatingWidget", "_ref", "theme", "position", "bottom", "spacing", "right", "width", "borderRadius", "background", "border", "boxShadow", "<PERSON><PERSON>ilter", "zIndex", "breakpoints", "down", "_c", "HeaderBox", "_ref2", "padding", "borderBottom", "palette", "divider", "cursor", "backgroundColor", "_c2", "ContentBox", "_ref3", "_c3", "GoalTrackerWidget", "_s", "expanded", "setExpanded", "goals", "setGoals", "completedGoals", "setCompletedGoals", "defaultGoals", "id", "title", "description", "category", "points", "completed", "savedGoals", "localStorage", "getItem", "savedCompleted", "JSON", "parse", "setItem", "stringify", "toggleGoal", "goalId", "updatedGoals", "map", "goal", "filter", "totalPoints", "reduce", "sum", "earnedPoints", "progress", "getCategoryColor", "colors", "elevation", "children", "onClick", "display", "alignItems", "justifyContent", "sx", "color", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "mt", "value", "height", "Math", "round", "in", "dense", "button", "mb", "min<PERSON><PERSON><PERSON>", "primary", "textDecoration", "fontSize", "secondary", "secondaryTypographyProps", "p", "textAlign", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/GoalTrackerWidget.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Box, \n  Typography, \n  LinearProgress, \n  IconButton,\n  Collapse,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Chip,\n  Paper\n} from '@mui/material';\nimport { \n  Flag as FlagIcon,\n  ExpandMore as ExpandIcon,\n  ExpandLess as CollapseIcon,\n  CheckCircle as CheckIcon,\n  RadioButtonUnchecked as UncheckedIcon,\n  TrendingUp as TrendingIcon\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { UXStorageManager } from './UXCard';\n\nconst FloatingWidget = styled(Paper)(({ theme }) => ({\n  position: 'fixed',\n  bottom: theme.spacing(3),\n  right: theme.spacing(3),\n  width: 300,\n  borderRadius: theme.spacing(2),\n  background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.05) 100%)',\n  border: '1px solid rgba(76, 175, 80, 0.3)',\n  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n  backdropFilter: 'blur(10px)',\n  zIndex: 998,\n  [theme.breakpoints.down('md')]: {\n    right: theme.spacing(2),\n    width: 280,\n  }\n}));\n\nconst HeaderBox = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(2),\n  borderBottom: `1px solid ${theme.palette.divider}`,\n  cursor: 'pointer',\n  '&:hover': {\n    backgroundColor: 'rgba(0,0,0,0.02)',\n  }\n}));\n\nconst ContentBox = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(0, 2, 2, 2),\n}));\n\nconst GoalTrackerWidget = () => {\n  const [expanded, setExpanded] = useState(false);\n  const [goals, setGoals] = useState([]);\n  const [completedGoals, setCompletedGoals] = useState([]);\n\n  const defaultGoals = [\n    {\n      id: 'complete_profile',\n      title: 'Complete Your Profile',\n      description: 'Add photo, bio, and contact info',\n      category: 'Profile',\n      points: 20,\n      completed: false\n    },\n    {\n      id: 'add_social_links',\n      title: 'Connect Social Media',\n      description: 'Add at least 3 social media links',\n      category: 'Links',\n      points: 15,\n      completed: false\n    },\n    {\n      id: 'customize_appearance',\n      title: 'Customize Appearance',\n      description: 'Choose theme and layout options',\n      category: 'Design',\n      points: 10,\n      completed: false\n    },\n    {\n      id: 'share_profile',\n      title: 'Share Your Profile',\n      description: 'Share your profile link 5 times',\n      category: 'Growth',\n      points: 25,\n      completed: false\n    },\n    {\n      id: 'check_analytics',\n      title: 'Review Analytics',\n      description: 'Check your profile analytics',\n      category: 'Insights',\n      points: 15,\n      completed: false\n    }\n  ];\n\n  useEffect(() => {\n    // Load goals from localStorage or use defaults\n    const savedGoals = localStorage.getItem('user_goals');\n    const savedCompleted = localStorage.getItem('completed_goals');\n    \n    if (savedGoals) {\n      setGoals(JSON.parse(savedGoals));\n    } else {\n      setGoals(defaultGoals);\n      localStorage.setItem('user_goals', JSON.stringify(defaultGoals));\n    }\n    \n    if (savedCompleted) {\n      setCompletedGoals(JSON.parse(savedCompleted));\n    }\n  }, []);\n\n  const toggleGoal = (goalId) => {\n    const updatedGoals = goals.map(goal => \n      goal.id === goalId ? { ...goal, completed: !goal.completed } : goal\n    );\n    \n    setGoals(updatedGoals);\n    localStorage.setItem('user_goals', JSON.stringify(updatedGoals));\n    \n    // Update completed goals for points calculation\n    const completed = updatedGoals.filter(goal => goal.completed);\n    setCompletedGoals(completed);\n    localStorage.setItem('completed_goals', JSON.stringify(completed));\n  };\n\n  const totalPoints = goals.reduce((sum, goal) => sum + goal.points, 0);\n  const earnedPoints = completedGoals.reduce((sum, goal) => sum + goal.points, 0);\n  const progress = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0;\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      'Profile': '#2196F3',\n      'Links': '#4CAF50',\n      'Design': '#FF9800',\n      'Growth': '#9C27B0',\n      'Insights': '#F44336'\n    };\n    return colors[category] || '#757575';\n  };\n\n  return (\n    <FloatingWidget elevation={8}>\n      <HeaderBox onClick={() => setExpanded(!expanded)}>\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n          <Box display=\"flex\" alignItems=\"center\">\n            <FlagIcon sx={{ color: 'success.main', mr: 1 }} />\n            <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n              Goal Tracker\n            </Typography>\n          </Box>\n          <Box display=\"flex\" alignItems=\"center\">\n            <Chip \n              label={`${earnedPoints}/${totalPoints} pts`}\n              size=\"small\"\n              color=\"success\"\n              variant=\"outlined\"\n              sx={{ mr: 1 }}\n            />\n            {expanded ? <CollapseIcon /> : <ExpandIcon />}\n          </Box>\n        </Box>\n        \n        <Box mt={1}>\n          <LinearProgress \n            variant=\"determinate\" \n            value={progress} \n            sx={{ \n              height: 6, \n              borderRadius: 3,\n              backgroundColor: 'rgba(76, 175, 80, 0.2)',\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: 'success.main',\n              }\n            }} \n          />\n          <Typography variant=\"caption\" color=\"text.secondary\" mt={0.5}>\n            {Math.round(progress)}% Complete\n          </Typography>\n        </Box>\n      </HeaderBox>\n\n      <Collapse in={expanded}>\n        <ContentBox>\n          <List dense>\n            {goals.map((goal) => (\n              <ListItem \n                key={goal.id}\n                button\n                onClick={() => toggleGoal(goal.id)}\n                sx={{ \n                  borderRadius: 1,\n                  mb: 0.5,\n                  '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' }\n                }}\n              >\n                <ListItemIcon sx={{ minWidth: 32 }}>\n                  {goal.completed ? (\n                    <CheckIcon color=\"success\" />\n                  ) : (\n                    <UncheckedIcon color=\"action\" />\n                  )}\n                </ListItemIcon>\n                <ListItemText\n                  primary={\n                    <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                      <Typography \n                        variant=\"body2\" \n                        sx={{ \n                          textDecoration: goal.completed ? 'line-through' : 'none',\n                          color: goal.completed ? 'text.secondary' : 'text.primary'\n                        }}\n                      >\n                        {goal.title}\n                      </Typography>\n                      <Chip \n                        label={`${goal.points}pts`}\n                        size=\"small\"\n                        sx={{ \n                          backgroundColor: getCategoryColor(goal.category),\n                          color: 'white',\n                          fontSize: '0.7rem',\n                          height: 20\n                        }}\n                      />\n                    </Box>\n                  }\n                  secondary={goal.description}\n                  secondaryTypographyProps={{ \n                    variant: 'caption',\n                    color: goal.completed ? 'text.disabled' : 'text.secondary'\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n          \n          {progress === 100 && (\n            <Box \n              mt={2} \n              p={2} \n              sx={{ \n                backgroundColor: 'rgba(76, 175, 80, 0.1)',\n                borderRadius: 1,\n                textAlign: 'center'\n              }}\n            >\n              <TrendingIcon color=\"success\" sx={{ mb: 1 }} />\n              <Typography variant=\"body2\" color=\"success.main\" fontWeight=\"bold\">\n                🎉 All Goals Complete!\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                You're making great progress!\n              </Typography>\n            </Box>\n          )}\n        </ContentBox>\n      </Collapse>\n    </FloatingWidget>\n  );\n};\n\nexport default GoalTrackerWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,UAAU,EACVC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,UAAU,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,WAAW,IAAIC,SAAS,EACxBC,oBAAoB,IAAIC,aAAa,EACrCC,UAAU,IAAIC,YAAY,QACrB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,cAAc,GAAGJ,MAAM,CAACb,KAAK,CAAC,CAACkB,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IACnDE,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACxBC,KAAK,EAAEJ,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACvBE,KAAK,EAAE,GAAG;IACVC,YAAY,EAAEN,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IAC9BI,UAAU,EAAE,mFAAmF;IAC/FC,MAAM,EAAE,kCAAkC;IAC1CC,SAAS,EAAE,4BAA4B;IACvCC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,GAAG;IACX,CAACX,KAAK,CAACY,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;MAC9BT,KAAK,EAAEJ,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;MACvBE,KAAK,EAAE;IACT;EACF,CAAC;AAAA,CAAC,CAAC;AAACS,EAAA,GAfEhB,cAAc;AAiBpB,MAAMiB,SAAS,GAAGrB,MAAM,CAACvB,GAAG,CAAC,CAAC6C,KAAA;EAAA,IAAC;IAAEhB;EAAM,CAAC,GAAAgB,KAAA;EAAA,OAAM;IAC5CC,OAAO,EAAEjB,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACzBe,YAAY,EAAE,aAAalB,KAAK,CAACmB,OAAO,CAACC,OAAO,EAAE;IAClDC,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE;MACTC,eAAe,EAAE;IACnB;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GAPER,SAAS;AASf,MAAMS,UAAU,GAAG9B,MAAM,CAACvB,GAAG,CAAC,CAACsD,KAAA;EAAA,IAAC;IAAEzB;EAAM,CAAC,GAAAyB,KAAA;EAAA,OAAM;IAC7CR,OAAO,EAAEjB,KAAK,CAACG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EACnC,CAAC;AAAA,CAAC,CAAC;AAACuB,GAAA,GAFEF,UAAU;AAIhB,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgE,cAAc,EAAEC,iBAAiB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAMkE,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,mCAAmC;IAChDC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,sBAAsB;IAC1BC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,8BAA8B;IAC3CC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE;EACb,CAAC,CACF;EAEDvE,SAAS,CAAC,MAAM;IACd;IACA,MAAMwE,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAE9D,IAAIF,UAAU,EAAE;MACdV,QAAQ,CAACc,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAC;IAClC,CAAC,MAAM;MACLV,QAAQ,CAACG,YAAY,CAAC;MACtBQ,YAAY,CAACK,OAAO,CAAC,YAAY,EAAEF,IAAI,CAACG,SAAS,CAACd,YAAY,CAAC,CAAC;IAClE;IAEA,IAAIU,cAAc,EAAE;MAClBX,iBAAiB,CAACY,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,UAAU,GAAIC,MAAM,IAAK;IAC7B,MAAMC,YAAY,GAAGrB,KAAK,CAACsB,GAAG,CAACC,IAAI,IACjCA,IAAI,CAAClB,EAAE,KAAKe,MAAM,GAAG;MAAE,GAAGG,IAAI;MAAEb,SAAS,EAAE,CAACa,IAAI,CAACb;IAAU,CAAC,GAAGa,IACjE,CAAC;IAEDtB,QAAQ,CAACoB,YAAY,CAAC;IACtBT,YAAY,CAACK,OAAO,CAAC,YAAY,EAAEF,IAAI,CAACG,SAAS,CAACG,YAAY,CAAC,CAAC;;IAEhE;IACA,MAAMX,SAAS,GAAGW,YAAY,CAACG,MAAM,CAACD,IAAI,IAAIA,IAAI,CAACb,SAAS,CAAC;IAC7DP,iBAAiB,CAACO,SAAS,CAAC;IAC5BE,YAAY,CAACK,OAAO,CAAC,iBAAiB,EAAEF,IAAI,CAACG,SAAS,CAACR,SAAS,CAAC,CAAC;EACpE,CAAC;EAED,MAAMe,WAAW,GAAGzB,KAAK,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEJ,IAAI,KAAKI,GAAG,GAAGJ,IAAI,CAACd,MAAM,EAAE,CAAC,CAAC;EACrE,MAAMmB,YAAY,GAAG1B,cAAc,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEJ,IAAI,KAAKI,GAAG,GAAGJ,IAAI,CAACd,MAAM,EAAE,CAAC,CAAC;EAC/E,MAAMoB,QAAQ,GAAGJ,WAAW,GAAG,CAAC,GAAIG,YAAY,GAAGH,WAAW,GAAI,GAAG,GAAG,CAAC;EAEzE,MAAMK,gBAAgB,GAAItB,QAAQ,IAAK;IACrC,MAAMuB,MAAM,GAAG;MACb,SAAS,EAAE,SAAS;MACpB,OAAO,EAAE,SAAS;MAClB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACvB,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,oBACE1C,OAAA,CAACC,cAAc;IAACiE,SAAS,EAAE,CAAE;IAAAC,QAAA,gBAC3BnE,OAAA,CAACkB,SAAS;MAACkD,OAAO,EAAEA,CAAA,KAAMnC,WAAW,CAAC,CAACD,QAAQ,CAAE;MAAAmC,QAAA,gBAC/CnE,OAAA,CAAC1B,GAAG;QAAC+F,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAAAJ,QAAA,gBACpEnE,OAAA,CAAC1B,GAAG;UAAC+F,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAAAH,QAAA,gBACrCnE,OAAA,CAACd,QAAQ;YAACsF,EAAE,EAAE;cAAEC,KAAK,EAAE,cAAc;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD9E,OAAA,CAACzB,UAAU;YAACwG,OAAO,EAAC,WAAW;YAACC,UAAU,EAAC,MAAM;YAAAb,QAAA,EAAC;UAElD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN9E,OAAA,CAAC1B,GAAG;UAAC+F,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAAAH,QAAA,gBACrCnE,OAAA,CAACjB,IAAI;YACHkG,KAAK,EAAE,GAAGnB,YAAY,IAAIH,WAAW,MAAO;YAC5CuB,IAAI,EAAC,OAAO;YACZT,KAAK,EAAC,SAAS;YACfM,OAAO,EAAC,UAAU;YAClBP,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACD9C,QAAQ,gBAAGhC,OAAA,CAACV,YAAY;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG9E,OAAA,CAACZ,UAAU;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA,CAAC1B,GAAG;QAAC6G,EAAE,EAAE,CAAE;QAAAhB,QAAA,gBACTnE,OAAA,CAACxB,cAAc;UACbuG,OAAO,EAAC,aAAa;UACrBK,KAAK,EAAErB,QAAS;UAChBS,EAAE,EAAE;YACFa,MAAM,EAAE,CAAC;YACT5E,YAAY,EAAE,CAAC;YACfgB,eAAe,EAAE,wBAAwB;YACzC,0BAA0B,EAAE;cAC1BA,eAAe,EAAE;YACnB;UACF;QAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF9E,OAAA,CAACzB,UAAU;UAACwG,OAAO,EAAC,SAAS;UAACN,KAAK,EAAC,gBAAgB;UAACU,EAAE,EAAE,GAAI;UAAAhB,QAAA,GAC1DmB,IAAI,CAACC,KAAK,CAACxB,QAAQ,CAAC,EAAC,YACxB;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZ9E,OAAA,CAACtB,QAAQ;MAAC8G,EAAE,EAAExD,QAAS;MAAAmC,QAAA,eACrBnE,OAAA,CAAC2B,UAAU;QAAAwC,QAAA,gBACTnE,OAAA,CAACrB,IAAI;UAAC8G,KAAK;UAAAtB,QAAA,EACRjC,KAAK,CAACsB,GAAG,CAAEC,IAAI,iBACdzD,OAAA,CAACpB,QAAQ;YAEP8G,MAAM;YACNtB,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAACI,IAAI,CAAClB,EAAE,CAAE;YACnCiC,EAAE,EAAE;cACF/D,YAAY,EAAE,CAAC;cACfkF,EAAE,EAAE,GAAG;cACP,SAAS,EAAE;gBAAElE,eAAe,EAAE;cAAmB;YACnD,CAAE;YAAA0C,QAAA,gBAEFnE,OAAA,CAACnB,YAAY;cAAC2F,EAAE,EAAE;gBAAEoB,QAAQ,EAAE;cAAG,CAAE;cAAAzB,QAAA,EAChCV,IAAI,CAACb,SAAS,gBACb5C,OAAA,CAACR,SAAS;gBAACiF,KAAK,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE7B9E,OAAA,CAACN,aAAa;gBAAC+E,KAAK,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAChC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACf9E,OAAA,CAAClB,YAAY;cACX+G,OAAO,eACL7F,OAAA,CAAC1B,GAAG;gBAAC+F,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAAJ,QAAA,gBACpEnE,OAAA,CAACzB,UAAU;kBACTwG,OAAO,EAAC,OAAO;kBACfP,EAAE,EAAE;oBACFsB,cAAc,EAAErC,IAAI,CAACb,SAAS,GAAG,cAAc,GAAG,MAAM;oBACxD6B,KAAK,EAAEhB,IAAI,CAACb,SAAS,GAAG,gBAAgB,GAAG;kBAC7C,CAAE;kBAAAuB,QAAA,EAEDV,IAAI,CAACjB;gBAAK;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACb9E,OAAA,CAACjB,IAAI;kBACHkG,KAAK,EAAE,GAAGxB,IAAI,CAACd,MAAM,KAAM;kBAC3BuC,IAAI,EAAC,OAAO;kBACZV,EAAE,EAAE;oBACF/C,eAAe,EAAEuC,gBAAgB,CAACP,IAAI,CAACf,QAAQ,CAAC;oBAChD+B,KAAK,EAAE,OAAO;oBACdsB,QAAQ,EAAE,QAAQ;oBAClBV,MAAM,EAAE;kBACV;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;cACDkB,SAAS,EAAEvC,IAAI,CAAChB,WAAY;cAC5BwD,wBAAwB,EAAE;gBACxBlB,OAAO,EAAE,SAAS;gBAClBN,KAAK,EAAEhB,IAAI,CAACb,SAAS,GAAG,eAAe,GAAG;cAC5C;YAAE;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA7CGrB,IAAI,CAAClB,EAAE;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CJ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAENf,QAAQ,KAAK,GAAG,iBACf/D,OAAA,CAAC1B,GAAG;UACF6G,EAAE,EAAE,CAAE;UACNe,CAAC,EAAE,CAAE;UACL1B,EAAE,EAAE;YACF/C,eAAe,EAAE,wBAAwB;YACzChB,YAAY,EAAE,CAAC;YACf0F,SAAS,EAAE;UACb,CAAE;UAAAhC,QAAA,gBAEFnE,OAAA,CAACJ,YAAY;YAAC6E,KAAK,EAAC,SAAS;YAACD,EAAE,EAAE;cAAEmB,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C9E,OAAA,CAACzB,UAAU;YAACwG,OAAO,EAAC,OAAO;YAACN,KAAK,EAAC,cAAc;YAACO,UAAU,EAAC,MAAM;YAAAb,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9E,OAAA,CAACzB,UAAU;YAACwG,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAErB,CAAC;AAAC/C,EAAA,CArNID,iBAAiB;AAAAsE,GAAA,GAAjBtE,iBAAiB;AAuNvB,eAAeA,iBAAiB;AAAC,IAAAb,EAAA,EAAAS,GAAA,EAAAG,GAAA,EAAAuE,GAAA;AAAAC,YAAA,CAAApF,EAAA;AAAAoF,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}