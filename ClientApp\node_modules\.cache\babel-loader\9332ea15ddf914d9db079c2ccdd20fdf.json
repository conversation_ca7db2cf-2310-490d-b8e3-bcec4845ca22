{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\InspirationCarousel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, IconButton, Card, CardContent, Chip, Avatar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Work as WorkIcon, Event as EventIcon, Store as StoreIcon, School as SchoolIcon } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CarouselContainer = styled(Box)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'relative',\n    overflow: 'hidden',\n    borderRadius: theme.spacing(1)\n  };\n});\n_c = CarouselContainer;\nconst CarouselContent = styled(Box)(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    display: 'flex',\n    transition: 'transform 0.3s ease',\n    gap: theme.spacing(2)\n  };\n});\n_c2 = CarouselContent;\nconst ExampleCard = styled(Card)(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    minWidth: 280,\n    borderRadius: theme.spacing(1.5),\n    background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)',\n    border: '1px solid rgba(0,0,0,0.08)'\n  };\n});\n_c3 = ExampleCard;\nconst InspirationCarousel = _ref4 => {\n  _s();\n  let {\n    onExampleClick\n  } = _ref4;\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const inspirationExamples = [{\n    id: 'portfolio_showcase',\n    title: 'Portfolio Showcase',\n    description: 'Creative professionals showcase their work and attract clients with stunning visual portfolios.',\n    icon: /*#__PURE__*/_jsxDEV(WorkIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this),\n    color: '#2196F3',\n    chips: ['Portfolio', 'Creative', 'Freelance'],\n    stats: '3.2x more engagement'\n  }, {\n    id: 'business_networking',\n    title: 'Business Networking',\n    description: 'Entrepreneurs share business links and contact information for efficient networking.',\n    icon: /*#__PURE__*/_jsxDEV(StoreIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    color: '#4CAF50',\n    chips: ['Business', 'Networking', 'Contact'],\n    stats: '85% faster connections'\n  }, {\n    id: 'event_promotion',\n    title: 'Event Promotion',\n    description: 'Event organizers use custom links to promote and track event registrations effectively.',\n    icon: /*#__PURE__*/_jsxDEV(EventIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    color: '#FF9800',\n    chips: ['Events', 'Marketing', 'Tracking'],\n    stats: '60% higher attendance'\n  }, {\n    id: 'education_resources',\n    title: 'Education Hub',\n    description: 'Educators create resource hubs with course materials, schedules, and student portals.',\n    icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this),\n    color: '#9C27B0',\n    chips: ['Education', 'Resources', 'Students'],\n    stats: '40% better engagement'\n  }];\n  const nextSlide = () => {\n    setCurrentIndex(prev => prev === inspirationExamples.length - 1 ? 0 : prev + 1);\n  };\n  const prevSlide = () => {\n    setCurrentIndex(prev => prev === 0 ? inspirationExamples.length - 1 : prev - 1);\n  };\n  const handleExampleClick = example => {\n    if (onExampleClick) {\n      onExampleClick(example);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: \"inspiration_carousel\",\n    type: \"inspiration\",\n    title: \"\\uD83C\\uDF1F Inspiration Gallery\",\n    description: \"See how others are using iDigics to achieve their goals.\",\n    resetAfterDays: 3,\n    animation: \"fade\",\n    children: /*#__PURE__*/_jsxDEV(CarouselContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: prevSlide,\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [currentIndex + 1, \" of \", inspirationExamples.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: nextSlide,\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CarouselContent, {\n        sx: {\n          transform: `translateX(-${currentIndex * 300}px)`\n        },\n        children: inspirationExamples.map((example, index) => /*#__PURE__*/_jsxDEV(ExampleCard, {\n          onClick: () => handleExampleClick(example),\n          sx: {\n            cursor: 'pointer'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  backgroundColor: example.color,\n                  width: 40,\n                  height: 40,\n                  mr: 2\n                },\n                children: example.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h3\",\n                  children: example.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"primary\",\n                  children: example.stats\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              paragraph: true,\n              children: example.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: example.chips.map((chip, chipIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: chip,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, chipIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)\n        }, example.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(InspirationCarousel, \"tusBbsahUVevXfyh6oH5R6YDC9Q=\");\n_c4 = InspirationCarousel;\nexport default InspirationCarousel;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"CarouselContainer\");\n$RefreshReg$(_c2, \"CarouselContent\");\n$RefreshReg$(_c3, \"ExampleCard\");\n$RefreshReg$(_c4, \"InspirationCarousel\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "IconButton", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "Avatar", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Work", "WorkIcon", "Event", "EventIcon", "Store", "StoreIcon", "School", "SchoolIcon", "styled", "UXCard", "jsxDEV", "_jsxDEV", "CarouselContainer", "_ref", "theme", "position", "overflow", "borderRadius", "spacing", "_c", "CarouselContent", "_ref2", "display", "transition", "gap", "_c2", "ExampleCard", "_ref3", "min<PERSON><PERSON><PERSON>", "background", "border", "_c3", "InspirationCarousel", "_ref4", "_s", "onExampleClick", "currentIndex", "setCurrentIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "chips", "stats", "nextSlide", "prev", "length", "prevSlide", "handleExampleClick", "example", "type", "resetAfterDays", "animation", "children", "justifyContent", "alignItems", "mb", "onClick", "size", "variant", "sx", "transform", "map", "index", "cursor", "backgroundColor", "width", "height", "mr", "component", "paragraph", "chip", "chipIndex", "label", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/InspirationCarousel.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Box, \n  Typography, \n  IconButton, \n  Card, \n  CardContent,\n  Chip,\n  Avatar\n} from '@mui/material';\nimport { \n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Work as WorkIcon,\n  Event as EventIcon,\n  Store as StoreIcon,\n  School as SchoolIcon\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\n\nconst CarouselContainer = styled(Box)(({ theme }) => ({\n  position: 'relative',\n  overflow: 'hidden',\n  borderRadius: theme.spacing(1),\n}));\n\nconst CarouselContent = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  transition: 'transform 0.3s ease',\n  gap: theme.spacing(2),\n}));\n\nconst ExampleCard = styled(Card)(({ theme }) => ({\n  minWidth: 280,\n  borderRadius: theme.spacing(1.5),\n  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)',\n  border: '1px solid rgba(0,0,0,0.08)',\n}));\n\nconst InspirationCarousel = ({ onExampleClick }) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const inspirationExamples = [\n    {\n      id: 'portfolio_showcase',\n      title: 'Portfolio Showcase',\n      description: 'Creative professionals showcase their work and attract clients with stunning visual portfolios.',\n      icon: <WorkIcon />,\n      color: '#2196F3',\n      chips: ['Portfolio', 'Creative', 'Freelance'],\n      stats: '3.2x more engagement'\n    },\n    {\n      id: 'business_networking',\n      title: 'Business Networking',\n      description: 'Entrepreneurs share business links and contact information for efficient networking.',\n      icon: <StoreIcon />,\n      color: '#4CAF50',\n      chips: ['Business', 'Networking', 'Contact'],\n      stats: '85% faster connections'\n    },\n    {\n      id: 'event_promotion',\n      title: 'Event Promotion',\n      description: 'Event organizers use custom links to promote and track event registrations effectively.',\n      icon: <EventIcon />,\n      color: '#FF9800',\n      chips: ['Events', 'Marketing', 'Tracking'],\n      stats: '60% higher attendance'\n    },\n    {\n      id: 'education_resources',\n      title: 'Education Hub',\n      description: 'Educators create resource hubs with course materials, schedules, and student portals.',\n      icon: <SchoolIcon />,\n      color: '#9C27B0',\n      chips: ['Education', 'Resources', 'Students'],\n      stats: '40% better engagement'\n    }\n  ];\n\n  const nextSlide = () => {\n    setCurrentIndex((prev) => \n      prev === inspirationExamples.length - 1 ? 0 : prev + 1\n    );\n  };\n\n  const prevSlide = () => {\n    setCurrentIndex((prev) => \n      prev === 0 ? inspirationExamples.length - 1 : prev - 1\n    );\n  };\n\n  const handleExampleClick = (example) => {\n    if (onExampleClick) {\n      onExampleClick(example);\n    }\n  };\n\n  return (\n    <UXCard\n      id=\"inspiration_carousel\"\n      type=\"inspiration\"\n      title=\"🌟 Inspiration Gallery\"\n      description=\"See how others are using iDigics to achieve their goals.\"\n      resetAfterDays={3}\n      animation=\"fade\"\n    >\n      <CarouselContainer>\n        <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n          <IconButton onClick={prevSlide} size=\"small\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {currentIndex + 1} of {inspirationExamples.length}\n          </Typography>\n          <IconButton onClick={nextSlide} size=\"small\">\n            <ArrowForwardIcon />\n          </IconButton>\n        </Box>\n\n        <CarouselContent\n          sx={{\n            transform: `translateX(-${currentIndex * 300}px)`,\n          }}\n        >\n          {inspirationExamples.map((example, index) => (\n            <ExampleCard \n              key={example.id}\n              onClick={() => handleExampleClick(example)}\n              sx={{ cursor: 'pointer' }}\n            >\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" mb={2}>\n                  <Avatar \n                    sx={{ \n                      backgroundColor: example.color,\n                      width: 40,\n                      height: 40,\n                      mr: 2\n                    }}\n                  >\n                    {example.icon}\n                  </Avatar>\n                  <Box>\n                    <Typography variant=\"h6\" component=\"h3\">\n                      {example.title}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"primary\">\n                      {example.stats}\n                    </Typography>\n                  </Box>\n                </Box>\n                \n                <Typography variant=\"body2\" color=\"text.secondary\" paragraph>\n                  {example.description}\n                </Typography>\n                \n                <Box>\n                  {example.chips.map((chip, chipIndex) => (\n                    <Chip\n                      key={chipIndex}\n                      label={chip}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      sx={{ mr: 0.5, mb: 0.5 }}\n                    />\n                  ))}\n                </Box>\n              </CardContent>\n            </ExampleCard>\n          ))}\n        </CarouselContent>\n      </CarouselContainer>\n    </UXCard>\n  );\n};\n\nexport default InspirationCarousel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,iBAAiB,GAAGJ,MAAM,CAACnB,GAAG,CAAC,CAACwB,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IACpDE,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC;EAC/B,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAJEP,iBAAiB;AAMvB,MAAMQ,eAAe,GAAGZ,MAAM,CAACnB,GAAG,CAAC,CAACgC,KAAA;EAAA,IAAC;IAAEP;EAAM,CAAC,GAAAO,KAAA;EAAA,OAAM;IAClDC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,qBAAqB;IACjCC,GAAG,EAAEV,KAAK,CAACI,OAAO,CAAC,CAAC;EACtB,CAAC;AAAA,CAAC,CAAC;AAACO,GAAA,GAJEL,eAAe;AAMrB,MAAMM,WAAW,GAAGlB,MAAM,CAAChB,IAAI,CAAC,CAACmC,KAAA;EAAA,IAAC;IAAEb;EAAM,CAAC,GAAAa,KAAA;EAAA,OAAM;IAC/CC,QAAQ,EAAE,GAAG;IACbX,YAAY,EAAEH,KAAK,CAACI,OAAO,CAAC,GAAG,CAAC;IAChCW,UAAU,EAAE,+EAA+E;IAC3FC,MAAM,EAAE;EACV,CAAC;AAAA,CAAC,CAAC;AAACC,GAAA,GALEL,WAAW;AAOjB,MAAMM,mBAAmB,GAAGC,KAAA,IAAwB;EAAAC,EAAA;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAF,KAAA;EAC7C,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMkD,mBAAmB,GAAG,CAC1B;IACEC,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,iGAAiG;IAC9GC,IAAI,eAAE/B,OAAA,CAACV,QAAQ;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;IAC7CC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,EAAE,EAAE,qBAAqB;IACzBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,sFAAsF;IACnGC,IAAI,eAAE/B,OAAA,CAACN,SAAS;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC;IAC5CC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,yFAAyF;IACtGC,IAAI,eAAE/B,OAAA,CAACR,SAAS;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;IAC1CC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,EAAE,EAAE,qBAAqB;IACzBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uFAAuF;IACpGC,IAAI,eAAE/B,OAAA,CAACJ,UAAU;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;IAC7CC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBb,eAAe,CAAEc,IAAI,IACnBA,IAAI,KAAKb,mBAAmB,CAACc,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,CACvD,CAAC;EACH,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtBhB,eAAe,CAAEc,IAAI,IACnBA,IAAI,KAAK,CAAC,GAAGb,mBAAmB,CAACc,MAAM,GAAG,CAAC,GAAGD,IAAI,GAAG,CACvD,CAAC;EACH,CAAC;EAED,MAAMG,kBAAkB,GAAIC,OAAO,IAAK;IACtC,IAAIpB,cAAc,EAAE;MAClBA,cAAc,CAACoB,OAAO,CAAC;IACzB;EACF,CAAC;EAED,oBACE5C,OAAA,CAACF,MAAM;IACL8B,EAAE,EAAC,sBAAsB;IACzBiB,IAAI,EAAC,aAAa;IAClBhB,KAAK,EAAC,kCAAwB;IAC9BC,WAAW,EAAC,0DAA0D;IACtEgB,cAAc,EAAE,CAAE;IAClBC,SAAS,EAAC,MAAM;IAAAC,QAAA,eAEhBhD,OAAA,CAACC,iBAAiB;MAAA+C,QAAA,gBAChBhD,OAAA,CAACtB,GAAG;QAACiC,OAAO,EAAC,MAAM;QAACsC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,gBAC3EhD,OAAA,CAACpB,UAAU;UAACwE,OAAO,EAAEV,SAAU;UAACW,IAAI,EAAC,OAAO;UAAAL,QAAA,eAC1ChD,OAAA,CAACd,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbnC,OAAA,CAACrB,UAAU;UAAC2E,OAAO,EAAC,SAAS;UAAClB,KAAK,EAAC,gBAAgB;UAAAY,QAAA,GACjDvB,YAAY,GAAG,CAAC,EAAC,MAAI,EAACE,mBAAmB,CAACc,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACbnC,OAAA,CAACpB,UAAU;UAACwE,OAAO,EAAEb,SAAU;UAACc,IAAI,EAAC,OAAO;UAAAL,QAAA,eAC1ChD,OAAA,CAACZ,gBAAgB;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENnC,OAAA,CAACS,eAAe;QACd8C,EAAE,EAAE;UACFC,SAAS,EAAE,eAAe/B,YAAY,GAAG,GAAG;QAC9C,CAAE;QAAAuB,QAAA,EAEDrB,mBAAmB,CAAC8B,GAAG,CAAC,CAACb,OAAO,EAAEc,KAAK,kBACtC1D,OAAA,CAACe,WAAW;UAEVqC,OAAO,EAAEA,CAAA,KAAMT,kBAAkB,CAACC,OAAO,CAAE;UAC3CW,EAAE,EAAE;YAAEI,MAAM,EAAE;UAAU,CAAE;UAAAX,QAAA,eAE1BhD,OAAA,CAAClB,WAAW;YAAAkE,QAAA,gBACVhD,OAAA,CAACtB,GAAG;cAACiC,OAAO,EAAC,MAAM;cAACuC,UAAU,EAAC,QAAQ;cAACC,EAAE,EAAE,CAAE;cAAAH,QAAA,gBAC5ChD,OAAA,CAAChB,MAAM;gBACLuE,EAAE,EAAE;kBACFK,eAAe,EAAEhB,OAAO,CAACR,KAAK;kBAC9ByB,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE,EAAE;kBACVC,EAAE,EAAE;gBACN,CAAE;gBAAAf,QAAA,EAEDJ,OAAO,CAACb;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACTnC,OAAA,CAACtB,GAAG;gBAAAsE,QAAA,gBACFhD,OAAA,CAACrB,UAAU;kBAAC2E,OAAO,EAAC,IAAI;kBAACU,SAAS,EAAC,IAAI;kBAAAhB,QAAA,EACpCJ,OAAO,CAACf;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbnC,OAAA,CAACrB,UAAU;kBAAC2E,OAAO,EAAC,SAAS;kBAAClB,KAAK,EAAC,SAAS;kBAAAY,QAAA,EAC1CJ,OAAO,CAACN;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnC,OAAA,CAACrB,UAAU;cAAC2E,OAAO,EAAC,OAAO;cAAClB,KAAK,EAAC,gBAAgB;cAAC6B,SAAS;cAAAjB,QAAA,EACzDJ,OAAO,CAACd;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEbnC,OAAA,CAACtB,GAAG;cAAAsE,QAAA,EACDJ,OAAO,CAACP,KAAK,CAACoB,GAAG,CAAC,CAACS,IAAI,EAAEC,SAAS,kBACjCnE,OAAA,CAACjB,IAAI;gBAEHqF,KAAK,EAAEF,IAAK;gBACZb,IAAI,EAAC,OAAO;gBACZC,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAEQ,EAAE,EAAE,GAAG;kBAAEZ,EAAE,EAAE;gBAAI;cAAE,GAJpBgB,SAAS;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GAzCTS,OAAO,CAAChB,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0CJ,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEb,CAAC;AAACZ,EAAA,CAzIIF,mBAAmB;AAAAgD,GAAA,GAAnBhD,mBAAmB;AA2IzB,eAAeA,mBAAmB;AAAC,IAAAb,EAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAiD,GAAA;AAAAC,YAAA,CAAA9D,EAAA;AAAA8D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}