{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\ProfileBoostCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button, Avatar, Chip } from '@mui/material';\nimport { PhotoCamera as PhotoIcon, Email as EmailIcon, Person as PersonIcon, Link as LinkIcon } from '@mui/icons-material';\nimport UXCard from './UXCard';\nimport { useProfile } from '../../Context/ProfileContext';\nimport useUXEnhancements from '../../hooks/useUXEnhancements';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileBoostCard = _ref => {\n  _s();\n  let {\n    onAction\n  } = _ref;\n  const {\n    profile\n  } = useProfile();\n  const {\n    calculateProfileCompletion\n  } = useUXEnhancements();\n  const completionPercentage = calculateProfileCompletion(profile);\n\n  // Don't show if profile is already complete\n  if (completionPercentage >= 90) return null;\n  const missingItems = [];\n  if (!(profile !== null && profile !== void 0 && profile.avatar)) {\n    missingItems.push({\n      icon: /*#__PURE__*/_jsxDEV(PhotoIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 13\n      }, this),\n      text: 'Add profile photo',\n      action: 'upload_photo'\n    });\n  }\n  if (!(profile !== null && profile !== void 0 && profile.bio) || profile.bio.trim().length < 10) {\n    missingItems.push({\n      icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 13\n      }, this),\n      text: 'Write a bio',\n      action: 'edit_bio'\n    });\n  }\n  if (!(profile !== null && profile !== void 0 && profile.emailVerified)) {\n    missingItems.push({\n      icon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 13\n      }, this),\n      text: 'Verify email',\n      action: 'verify_email'\n    });\n  }\n  const handleQuickAction = action => {\n    if (onAction) {\n      onAction(action);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: \"profile_boost_tips\",\n    type: \"tip\",\n    title: \"\\uD83D\\uDE80 Boost Your Profile\",\n    description: \"Complete profiles get 3\\xD7 more engagement! Here's what you can improve:\",\n    showProgress: true,\n    progressValue: completionPercentage,\n    progressLabel: \"Profile Completion\",\n    resetAfterDays: 3,\n    animation: \"slide\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      mt: 2,\n      children: [missingItems.slice(0, 3).map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        mb: 1,\n        sx: {\n          cursor: 'pointer',\n          p: 1,\n          borderRadius: 1,\n          '&:hover': {\n            backgroundColor: 'rgba(0,0,0,0.04)'\n          }\n        },\n        onClick: () => handleQuickAction(item.action),\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            color: 'primary.main',\n            mr: 2,\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this)), completionPercentage > 50 && /*#__PURE__*/_jsxDEV(Box, {\n        mt: 2,\n        children: /*#__PURE__*/_jsxDEV(Chip, {\n          label: `${completionPercentage}% Complete`,\n          color: \"primary\",\n          variant: \"outlined\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileBoostCard, \"TS+xp14+hkAhXrZubqRi9XADyf0=\", false, function () {\n  return [useProfile, useUXEnhancements];\n});\n_c = ProfileBoostCard;\nexport default ProfileBoostCard;\nvar _c;\n$RefreshReg$(_c, \"ProfileBoostCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Avatar", "Chip", "PhotoCamera", "PhotoIcon", "Email", "EmailIcon", "Person", "PersonIcon", "Link", "LinkIcon", "UXCard", "useProfile", "useUXEnhancements", "jsxDEV", "_jsxDEV", "ProfileBoostCard", "_ref", "_s", "onAction", "profile", "calculateProfileCompletion", "completionPercentage", "missingItems", "avatar", "push", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "action", "bio", "trim", "length", "emailVerified", "handleQuickAction", "id", "type", "title", "description", "showProgress", "progressValue", "progressLabel", "resetAfterDays", "animation", "children", "mt", "slice", "map", "item", "index", "display", "alignItems", "mb", "sx", "cursor", "p", "borderRadius", "backgroundColor", "onClick", "color", "mr", "variant", "label", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/ProfileBoostCard.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>, Typo<PERSON>, Button, Avatar, Chip } from '@mui/material';\nimport { \n  PhotoCamera as PhotoIcon,\n  Email as EmailIcon,\n  Person as PersonIcon,\n  Link as LinkIcon \n} from '@mui/icons-material';\nimport UXCard from './UXCard';\nimport { useProfile } from '../../Context/ProfileContext';\nimport useUXEnhancements from '../../hooks/useUXEnhancements';\n\nconst ProfileBoostCard = ({ onAction }) => {\n  const { profile } = useProfile();\n  const { calculateProfileCompletion } = useUXEnhancements();\n  \n  const completionPercentage = calculateProfileCompletion(profile);\n  \n  // Don't show if profile is already complete\n  if (completionPercentage >= 90) return null;\n\n  const missingItems = [];\n  \n  if (!profile?.avatar) {\n    missingItems.push({ \n      icon: <PhotoIcon />, \n      text: 'Add profile photo', \n      action: 'upload_photo' \n    });\n  }\n  \n  if (!profile?.bio || profile.bio.trim().length < 10) {\n    missingItems.push({ \n      icon: <PersonIcon />, \n      text: 'Write a bio', \n      action: 'edit_bio' \n    });\n  }\n  \n  if (!profile?.emailVerified) {\n    missingItems.push({ \n      icon: <EmailIcon />, \n      text: 'Verify email', \n      action: 'verify_email' \n    });\n  }\n\n  const handleQuickAction = (action) => {\n    if (onAction) {\n      onAction(action);\n    }\n  };\n\n  return (\n    <UXCard\n      id=\"profile_boost_tips\"\n      type=\"tip\"\n      title=\"🚀 Boost Your Profile\"\n      description=\"Complete profiles get 3× more engagement! Here's what you can improve:\"\n      showProgress={true}\n      progressValue={completionPercentage}\n      progressLabel=\"Profile Completion\"\n      resetAfterDays={3}\n      animation=\"slide\"\n    >\n      <Box mt={2}>\n        {missingItems.slice(0, 3).map((item, index) => (\n          <Box \n            key={index}\n            display=\"flex\" \n            alignItems=\"center\" \n            mb={1}\n            sx={{ \n              cursor: 'pointer',\n              p: 1,\n              borderRadius: 1,\n              '&:hover': { \n                backgroundColor: 'rgba(0,0,0,0.04)' \n              }\n            }}\n            onClick={() => handleQuickAction(item.action)}\n          >\n            <Box \n              sx={{ \n                color: 'primary.main', \n                mr: 2,\n                display: 'flex',\n                alignItems: 'center'\n              }}\n            >\n              {item.icon}\n            </Box>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              {item.text}\n            </Typography>\n          </Box>\n        ))}\n        \n        {completionPercentage > 50 && (\n          <Box mt={2}>\n            <Chip \n              label={`${completionPercentage}% Complete`}\n              color=\"primary\"\n              variant=\"outlined\"\n              size=\"small\"\n            />\n          </Box>\n        )}\n      </Box>\n    </UXCard>\n  );\n};\n\nexport default ProfileBoostCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AACrE,SACEC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,gBAAgB,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACpC,MAAM;IAAEG;EAAQ,CAAC,GAAGR,UAAU,CAAC,CAAC;EAChC,MAAM;IAAES;EAA2B,CAAC,GAAGR,iBAAiB,CAAC,CAAC;EAE1D,MAAMS,oBAAoB,GAAGD,0BAA0B,CAACD,OAAO,CAAC;;EAEhE;EACA,IAAIE,oBAAoB,IAAI,EAAE,EAAE,OAAO,IAAI;EAE3C,MAAMC,YAAY,GAAG,EAAE;EAEvB,IAAI,EAACH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,MAAM,GAAE;IACpBD,YAAY,CAACE,IAAI,CAAC;MAChBC,IAAI,eAAEX,OAAA,CAACX,SAAS;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE,mBAAmB;MACzBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,IAAI,EAACZ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEa,GAAG,KAAIb,OAAO,CAACa,GAAG,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,EAAE,EAAE;IACnDZ,YAAY,CAACE,IAAI,CAAC;MAChBC,IAAI,eAAEX,OAAA,CAACP,UAAU;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,IAAI,EAACZ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEgB,aAAa,GAAE;IAC3Bb,YAAY,CAACE,IAAI,CAAC;MAChBC,IAAI,eAAEX,OAAA,CAACT,SAAS;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEA,MAAMK,iBAAiB,GAAIL,MAAM,IAAK;IACpC,IAAIb,QAAQ,EAAE;MACZA,QAAQ,CAACa,MAAM,CAAC;IAClB;EACF,CAAC;EAED,oBACEjB,OAAA,CAACJ,MAAM;IACL2B,EAAE,EAAC,oBAAoB;IACvBC,IAAI,EAAC,KAAK;IACVC,KAAK,EAAC,iCAAuB;IAC7BC,WAAW,EAAC,2EAAwE;IACpFC,YAAY,EAAE,IAAK;IACnBC,aAAa,EAAErB,oBAAqB;IACpCsB,aAAa,EAAC,oBAAoB;IAClCC,cAAc,EAAE,CAAE;IAClBC,SAAS,EAAC,OAAO;IAAAC,QAAA,eAEjBhC,OAAA,CAACjB,GAAG;MAACkD,EAAE,EAAE,CAAE;MAAAD,QAAA,GACRxB,YAAY,CAAC0B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxCrC,OAAA,CAACjB,GAAG;QAEFuD,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBC,EAAE,EAAE,CAAE;QACNC,EAAE,EAAE;UACFC,MAAM,EAAE,SAAS;UACjBC,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACf,SAAS,EAAE;YACTC,eAAe,EAAE;UACnB;QACF,CAAE;QACFC,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACc,IAAI,CAACnB,MAAM,CAAE;QAAAe,QAAA,gBAE9ChC,OAAA,CAACjB,GAAG;UACF0D,EAAE,EAAE;YACFM,KAAK,EAAE,cAAc;YACrBC,EAAE,EAAE,CAAC;YACLV,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAEDI,IAAI,CAACzB;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNf,OAAA,CAAChB,UAAU;UAACiE,OAAO,EAAC,OAAO;UAACF,KAAK,EAAC,gBAAgB;UAAAf,QAAA,EAC/CI,IAAI,CAACpB;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA,GA1BRsB,KAAK;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2BP,CACN,CAAC,EAEDR,oBAAoB,GAAG,EAAE,iBACxBP,OAAA,CAACjB,GAAG;QAACkD,EAAE,EAAE,CAAE;QAAAD,QAAA,eACThC,OAAA,CAACb,IAAI;UACH+D,KAAK,EAAE,GAAG3C,oBAAoB,YAAa;UAC3CwC,KAAK,EAAC,SAAS;UACfE,OAAO,EAAC,UAAU;UAClBE,IAAI,EAAC;QAAO;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACZ,EAAA,CAnGIF,gBAAgB;EAAA,QACAJ,UAAU,EACSC,iBAAiB;AAAA;AAAAsD,EAAA,GAFpDnD,gBAAgB;AAqGtB,eAAeA,gBAAgB;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}