import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Close as CloseIcon,
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  VpnKey as VpnKeyIcon,
  PhoneAndroid as PhoneAndroidIcon,
  Visibility as VisibilityIcon,
  ExpandMore as ExpandMoreIcon,
  Lock as LockIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function SecurityBestPractices({ securityStatus, onSecurityAction }) {
  const [isVisible, setIsVisible] = useState(false);
  const [securityScore, setSecurityScore] = useState(0);
  const [expandedPanel, setExpandedPanel] = useState(false);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('securityBestPracticesVisible');
    const lastSecurityCheck = localStorage.getItem('lastSecurityCheck');
    const now = new Date().getTime();
    const daysSinceLastCheck = lastSecurityCheck ? (now - parseInt(lastSecurityCheck)) / (1000 * 60 * 60 * 24) : 999;
    
    // Show weekly or if security score is low
    const score = calculateSecurityScore();
    setIsVisible(isCardVisible !== 'false' && (daysSinceLastCheck > 7 || score < 70));
  }, [securityStatus]);

  const calculateSecurityScore = () => {
    // Mock security status - replace with real security data
    const mockStatus = {
      twoFactorAuth: false,
      strongPassword: true,
      recentPasswordChange: false,
      loginSessions: 3,
      suspiciousActivity: false,
      emailVerified: true,
      phoneVerified: false,
    };

    let score = 0;
    const maxScore = 100;
    
    if (mockStatus.twoFactorAuth) score += 30;
    if (mockStatus.strongPassword) score += 20;
    if (mockStatus.recentPasswordChange) score += 15;
    if (mockStatus.loginSessions <= 2) score += 10;
    if (!mockStatus.suspiciousActivity) score += 10;
    if (mockStatus.emailVerified) score += 10;
    if (mockStatus.phoneVerified) score += 5;
    
    setSecurityScore(score);
    return score;
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('securityBestPracticesVisible', 'false');
    localStorage.setItem('lastSecurityCheck', new Date().getTime().toString());
  };

  const handlePanelChange = (panel) => (event, isExpanded) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  const securityChecks = [
    {
      id: 'two-factor',
      title: 'Two-Factor Authentication',
      status: 'critical',
      completed: false,
      icon: <PhoneAndroidIcon sx={{ color: '#F44336' }} />,
      description: 'Add an extra layer of security to your account',
      impact: 'Prevents 99.9% of automated attacks',
      action: 'Enable 2FA',
      steps: [
        'Go to Security Settings',
        'Click "Enable Two-Factor Authentication"',
        'Scan QR code with authenticator app',
        'Enter verification code to confirm',
      ],
    },
    {
      id: 'password',
      title: 'Strong Password',
      status: 'good',
      completed: true,
      icon: <VpnKeyIcon sx={{ color: '#4CAF50' }} />,
      description: 'Your password meets security requirements',
      impact: 'Protects against brute force attacks',
      action: 'Update Password',
      steps: [
        'Use at least 12 characters',
        'Include uppercase and lowercase letters',
        'Add numbers and special characters',
        'Avoid common words or personal info',
      ],
    },
    {
      id: 'sessions',
      title: 'Active Sessions',
      status: 'warning',
      completed: false,
      icon: <VisibilityIcon sx={{ color: '#FF9800' }} />,
      description: 'You have 3 active login sessions',
      impact: 'Monitor for unauthorized access',
      action: 'Review Sessions',
      steps: [
        'Check all active devices',
        'Sign out unknown devices',
        'Enable login notifications',
        'Set up session timeouts',
      ],
    },
    {
      id: 'verification',
      title: 'Account Verification',
      status: 'warning',
      completed: false,
      icon: <CheckCircleIcon sx={{ color: '#FF9800' }} />,
      description: 'Phone number not verified',
      impact: 'Enables account recovery options',
      action: 'Verify Phone',
      steps: [
        'Add your phone number',
        'Receive verification SMS',
        'Enter verification code',
        'Enable SMS notifications',
      ],
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'critical': return '#F44336';
      case 'warning': return '#FF9800';
      case 'good': return '#4CAF50';
      default: return '#757575';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'critical': return <ErrorIcon sx={{ color: '#F44336', fontSize: 16 }} />;
      case 'warning': return <WarningIcon sx={{ color: '#FF9800', fontSize: 16 }} />;
      case 'good': return <CheckCircleIcon sx={{ color: '#4CAF50', fontSize: 16 }} />;
      default: return <ErrorIcon sx={{ color: '#757575', fontSize: 16 }} />;
    }
  };

  const criticalIssues = securityChecks.filter(check => check.status === 'critical' && !check.completed);
  const warningIssues = securityChecks.filter(check => check.status === 'warning' && !check.completed);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <SecurityIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Security Health Check
            </Typography>
            <Chip
              label={`${securityScore}% secure`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: securityScore > 70 ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)',
                color: 'white',
              }}
            />
          </Box>

          {/* Security Score */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Security Score</Typography>
              <Typography variant="body2">{securityScore}/100</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={securityScore}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: securityScore > 70 ? '#4CAF50' : securityScore > 40 ? '#FF9800' : '#F44336',
                  borderRadius: 4,
                },
              }}
            />
          </Box>

          {/* Critical Issues Alert */}
          {criticalIssues.length > 0 && (
            <Alert
              severity="error"
              sx={{ mb: 3, backgroundColor: 'rgba(244, 67, 54, 0.2)', color: 'white' }}
              action={
                <Button
                  size="small"
                  sx={{ color: 'white' }}
                  onClick={() => {
                    if (onSecurityAction) onSecurityAction(criticalIssues[0].id);
                  }}
                >
                  Fix Now
                </Button>
              }
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                {criticalIssues.length} Critical Security Issue{criticalIssues.length > 1 ? 's' : ''}
              </Typography>
              <Typography variant="body2">
                {criticalIssues[0].title} needs immediate attention
              </Typography>
            </Alert>
          )}

          {/* Security Checks */}
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Security Checklist
          </Typography>

          {securityChecks.map((check, index) => (
            <Accordion
              key={check.id}
              expanded={expandedPanel === check.id}
              onChange={handlePanelChange(check.id)}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.1)',
                color: 'white',
                mb: 1,
                borderRadius: 2,
                '&:before': { display: 'none' },
                '& .MuiAccordionSummary-root': {
                  borderRadius: 2,
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon sx={{ color: 'white' }} />}
                sx={{ py: 1 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <Avatar
                    sx={{
                      backgroundColor: `${getStatusColor(check.status)}20`,
                      mr: 2,
                      width: 32,
                      height: 32,
                    }}
                  >
                    {check.icon}
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {check.title}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.9 }}>
                      {check.description}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getStatusIcon(check.status)}
                    <Chip
                      label={check.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(check.status),
                        color: 'white',
                        fontSize: '10px',
                        height: 20,
                        textTransform: 'capitalize',
                      }}
                    />
                  </Box>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ pl: 6 }}>
                  <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                    💡 {check.impact}
                  </Typography>
                  
                  <Typography variant="caption" sx={{ fontWeight: 600, mb: 1, display: 'block' }}>
                    How to {check.action.toLowerCase()}:
                  </Typography>
                  <List sx={{ py: 0 }}>
                    {check.steps.map((step, stepIndex) => (
                      <ListItem key={stepIndex} sx={{ px: 0, py: 0.25 }}>
                        <ListItemIcon sx={{ minWidth: 20 }}>
                          <Box
                            sx={{
                              width: 16,
                              height: 16,
                              borderRadius: '50%',
                              backgroundColor: getStatusColor(check.status),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '10px',
                              fontWeight: 600,
                            }}
                          >
                            {stepIndex + 1}
                          </Box>
                        </ListItemIcon>
                        <ListItemText>
                          <Typography variant="caption">{step}</Typography>
                        </ListItemText>
                      </ListItem>
                    ))}
                  </List>
                  
                  <Button
                    size="small"
                    onClick={() => {
                      if (onSecurityAction) onSecurityAction(check.id);
                    }}
                    sx={{
                      backgroundColor: getStatusColor(check.status),
                      color: 'white',
                      '&:hover': { backgroundColor: getStatusColor(check.status), opacity: 0.8 },
                      borderRadius: 2,
                      mt: 1,
                    }}
                  >
                    {check.action}
                  </Button>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              Last checked: Just now
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<ShieldIcon />}
              onClick={() => {
                // Run security scan
                if (onSecurityAction) onSecurityAction('scan');
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Run Security Scan
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
