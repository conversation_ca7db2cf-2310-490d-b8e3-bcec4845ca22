import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Close as CloseIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Lightbulb as LightbulbIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function DataInterpretationHelper({ analyticsData, onTakeAction }) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [interpretations, setInterpretations] = useState([]);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('dataInterpretationHelperVisible');
    const hasData = analyticsData && Object.keys(analyticsData).length > 0;
    
    setIsVisible(isCardVisible !== 'false' && hasData);
    generateInterpretations();
  }, [analyticsData]);

  const generateInterpretations = () => {
    if (!analyticsData) return;

    // Mock data analysis - replace with real analytics interpretation
    const mockData = {
      views: { current: 245, previous: 189, trend: 'up' },
      clicks: { current: 34, previous: 28, trend: 'up' },
      clickRate: { current: 13.9, previous: 14.8, trend: 'down' },
      topCountries: ['United States', 'Canada', 'United Kingdom'],
      peakHours: ['2 PM', '7 PM', '10 AM'],
      topLinks: ['LinkedIn', 'Portfolio', 'Resume'],
    };

    const generated = [];

    // Views interpretation
    if (mockData.views.trend === 'up') {
      generated.push({
        type: 'positive',
        category: 'views',
        icon: <TrendingUpIcon sx={{ color: '#4CAF50' }} />,
        title: 'Growing Visibility! 📈',
        description: `Your profile views increased by ${Math.round(((mockData.views.current - mockData.views.previous) / mockData.views.previous) * 100)}% this period`,
        insights: [
          'Your content strategy is working',
          'Consider increasing posting frequency',
          'Share your profile on more platforms',
        ],
        action: 'Keep momentum going',
        severity: 'success',
      });
    }

    // Click rate interpretation
    if (mockData.clickRate.trend === 'down') {
      generated.push({
        type: 'warning',
        category: 'engagement',
        icon: <WarningIcon sx={{ color: '#FF9800' }} />,
        title: 'Click Rate Needs Attention ⚠️',
        description: `Click rate dropped to ${mockData.clickRate.current}% from ${mockData.clickRate.previous}%`,
        insights: [
          'Review your link titles and descriptions',
          'Consider reordering your most important links',
          'Test different call-to-action phrases',
        ],
        action: 'Optimize links',
        severity: 'warning',
      });
    }

    // Geographic insights
    generated.push({
      type: 'info',
      category: 'geographic',
      icon: <InfoIcon sx={{ color: '#2196F3' }} />,
      title: 'Global Reach Insights 🌍',
      description: `Top audiences from ${mockData.topCountries.slice(0, 2).join(' and ')}`,
      insights: [
        'Consider time zones for optimal posting',
        'Tailor content for these regions',
        'Explore networking in these areas',
      ],
      action: 'Expand reach',
      severity: 'info',
    });

    setInterpretations(generated);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('dataInterpretationHelperVisible', 'false');
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getTabContent = (tabIndex) => {
    const categories = ['views', 'engagement', 'geographic'];
    const categoryInterpretations = interpretations.filter(
      item => item.category === categories[tabIndex]
    );

    if (categoryInterpretations.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body2" color="text.secondary">
            No insights available for this category yet.
          </Typography>
        </Box>
      );
    }

    return categoryInterpretations.map((interpretation, index) => (
      <motion.div
        key={index}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <Alert
          severity={interpretation.severity}
          sx={{ mb: 2, borderRadius: 2 }}
          action={
            <Button
              size="small"
              onClick={() => {
                if (onTakeAction) onTakeAction(interpretation.category);
              }}
            >
              {interpretation.action}
            </Button>
          }
        >
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              {interpretation.title}
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              {interpretation.description}
            </Typography>
            <List sx={{ py: 0 }}>
              {interpretation.insights.map((insight, insightIndex) => (
                <ListItem key={insightIndex} sx={{ px: 0, py: 0.25 }}>
                  <ListItemIcon sx={{ minWidth: 20 }}>
                    <LightbulbIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
                  </ListItemIcon>
                  <ListItemText>
                    <Typography variant="caption">{insight}</Typography>
                  </ListItemText>
                </ListItem>
              ))}
            </List>
          </Box>
        </Alert>
      </motion.div>
    ));
  };

  const overallScore = Math.round(
    (interpretations.filter(i => i.type === 'positive').length / Math.max(interpretations.length, 1)) * 100
  );

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PsychologyIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Data Interpretation Helper
            </Typography>
            <Chip
              label={`${overallScore}% positive trends`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: overallScore > 60 ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 152, 0, 0.3)',
                color: 'white',
              }}
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1, opacity: 0.9 }}>
              Performance Health Score
            </Typography>
            <LinearProgress
              variant="determinate"
              value={overallScore}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: overallScore > 60 ? '#4CAF50' : '#FF9800',
                  borderRadius: 4,
                },
              }}
            />
          </Box>

          <Box sx={{ backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2, p: 0 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              sx={{
                '& .MuiTab-root': { color: 'rgba(255,255,255,0.7)', minHeight: 40 },
                '& .Mui-selected': { color: 'white' },
                '& .MuiTabs-indicator': { backgroundColor: 'white' },
              }}
            >
              <Tab label="Views" />
              <Tab label="Engagement" />
              <Tab label="Geographic" />
            </Tabs>

            <Box sx={{ p: 2 }}>
              {getTabContent(activeTab)}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              AI-powered insights updated daily
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                // Reset to show fresh insights next time
                localStorage.setItem('dataInterpretationHelperVisible', 'true');
                if (onTakeAction) onTakeAction('refresh');
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Refresh Insights
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
