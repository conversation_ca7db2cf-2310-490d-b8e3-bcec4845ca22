import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stepper,
  Step,
  StepLabel,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Rocket as RocketIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  Analytics as AnalyticsIcon,
  Public as PublicIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function UpgradeBenefitsShowcase({ currentPlan, onUpgrade }) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('upgradeBenefitsShowcaseVisible');
    const isFreePlan = currentPlan === 'Free' || currentPlan === 'Student';
    
    setIsVisible(isCardVisible !== 'false' && isFreePlan);
    
    // Cycle through features every 6 seconds
    const interval = setInterval(() => {
      setActiveFeature(prev => (prev + 1) % upgradeFeatures.length);
    }, 6000);
    
    return () => clearInterval(interval);
  }, [currentPlan]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('upgradeBenefitsShowcaseVisible', 'false');
  };

  const upgradeFeatures = [
    {
      icon: <AnalyticsIcon sx={{ color: '#2196F3' }} />,
      title: 'Advanced Analytics',
      description: 'Deep insights into your profile performance',
      benefits: [
        'Detailed click tracking',
        'Geographic audience data',
        'Peak activity times',
        'Conversion metrics',
      ],
      impact: '+40% better optimization',
      color: '#2196F3',
    },
    {
      icon: <PaletteIcon sx={{ color: '#9C27B0' }} />,
      title: 'Custom Branding',
      description: 'Make your profile uniquely yours',
      benefits: [
        'Custom color themes',
        'Remove IDigics branding',
        'Custom backgrounds',
        'Logo integration',
      ],
      impact: '+60% brand recognition',
      color: '#9C27B0',
    },
    {
      icon: <PublicIcon sx={{ color: '#4CAF50' }} />,
      title: 'Custom Domain',
      description: 'Professional URL for your brand',
      benefits: [
        'yourname.com/profile',
        'Better SEO ranking',
        'Professional appearance',
        'Easy to remember',
      ],
      impact: '+80% credibility boost',
      color: '#4CAF50',
    },
    {
      icon: <SecurityIcon sx={{ color: '#FF9800' }} />,
      title: 'Priority Support',
      description: '24/7 dedicated assistance',
      benefits: [
        'Live chat support',
        'Priority response',
        'Phone support',
        'Dedicated account manager',
      ],
      impact: '10x faster resolution',
      color: '#FF9800',
    },
  ];

  const currentFeature = upgradeFeatures[activeFeature];

  const planComparison = [
    { feature: 'Profile Links', free: '5', pro: 'Unlimited', business: 'Unlimited' },
    { feature: 'Analytics', free: 'Basic', pro: 'Advanced', business: 'Premium' },
    { feature: 'Custom Domain', free: '❌', pro: '✅', business: '✅' },
    { feature: 'Branding', free: 'IDigics', pro: 'Custom', business: 'White-label' },
    { feature: 'Support', free: 'Email', pro: 'Priority', business: '24/7 Dedicated' },
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
          overflow: 'hidden',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
            zIndex: 2,
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent sx={{ position: 'relative' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <RocketIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Unlock Your Full Potential
            </Typography>
            <Chip
              label="Limited Time"
              size="small"
              sx={{
                ml: 1,
                backgroundColor: '#FFD700',
                color: 'black',
                fontWeight: 600,
              }}
            />
          </Box>

          {/* Featured Benefit */}
          <motion.div
            key={activeFeature}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Box
              sx={{
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: 3,
                p: 3,
                mb: 3,
                border: `2px solid ${currentFeature.color}`,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    backgroundColor: `${currentFeature.color}20`,
                    mr: 2,
                    width: 48,
                    height: 48,
                  }}
                >
                  {currentFeature.icon}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {currentFeature.title}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    {currentFeature.description}
                  </Typography>
                </Box>
                <Chip
                  label={currentFeature.impact}
                  size="small"
                  sx={{
                    backgroundColor: currentFeature.color,
                    color: 'white',
                    fontWeight: 600,
                  }}
                />
              </Box>

              <List sx={{ py: 0 }}>
                {currentFeature.benefits.map((benefit, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 0.25 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <CheckCircleIcon sx={{ fontSize: 20, color: currentFeature.color }} />
                    </ListItemIcon>
                    <ListItemText>
                      <Typography variant="body2">{benefit}</Typography>
                    </ListItemText>
                  </ListItem>
                ))}
              </List>
            </Box>
          </motion.div>

          {/* Feature Navigation */}
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
            {upgradeFeatures.map((_, index) => (
              <Box
                key={index}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: index === activeFeature ? 'white' : 'rgba(255,255,255,0.3)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                }}
                onClick={() => setActiveFeature(index)}
              />
            ))}
          </Box>

          <Divider sx={{ backgroundColor: 'rgba(255,255,255,0.2)', mb: 3 }} />

          {/* Quick Comparison */}
          <Typography variant="subtitle2" sx={{ mb: 2, textAlign: 'center' }}>
            See What You're Missing
          </Typography>

          <Box sx={{ 
            backgroundColor: 'rgba(255,255,255,0.1)', 
            borderRadius: 2, 
            p: 2, 
            mb: 3 
          }}>
            {planComparison.slice(0, 3).map((item, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 1,
                  borderBottom: index < 2 ? '1px solid rgba(255,255,255,0.1)' : 'none',
                }}
              >
                <Typography variant="body2">{item.feature}</Typography>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Typography variant="caption" sx={{ opacity: 0.7, minWidth: 60 }}>
                    Free: {item.free}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#FFD700' }}>
                    Pro: {item.pro}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                Feature {activeFeature + 1} of {upgradeFeatures.length}
              </Typography>
              <Typography variant="caption" sx={{ display: 'block', opacity: 0.6 }}>
                Auto-cycling every 6s
              </Typography>
            </Box>
            <Button
              variant="contained"
              size="large"
              endIcon={<ArrowForwardIcon />}
              onClick={() => {
                if (onUpgrade) onUpgrade();
              }}
              sx={{
                backgroundColor: '#FFD700',
                color: 'black',
                '&:hover': { backgroundColor: '#FFC107' },
                borderRadius: 3,
                fontWeight: 600,
                px: 3,
              }}
            >
              Upgrade Now
            </Button>
          </Box>

          {/* Animated background elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -50,
              right: -50,
              width: 100,
              height: 100,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,215,0,0.1) 0%, transparent 70%)',
              animation: 'pulse 4s ease-in-out infinite',
              '@keyframes pulse': {
                '0%, 100%': { transform: 'scale(1)', opacity: 0.3 },
                '50%': { transform: 'scale(1.1)', opacity: 0.1 },
              },
            }}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
}
