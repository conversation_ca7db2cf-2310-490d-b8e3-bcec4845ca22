{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\BadgeRewardsCard.js\";\nimport React from 'react';\nimport { Box, Typography, Avatar, Grid } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BadgeAvatar = styled(Avatar)(_ref => {\n  let {\n    theme,\n    earned\n  } = _ref;\n  return {\n    width: 48,\n    height: 48,\n    fontSize: '1.5rem',\n    backgroundColor: earned ? theme.palette.success.main : theme.palette.grey[300],\n    color: earned ? 'white' : theme.palette.grey[600],\n    border: earned ? `2px solid ${theme.palette.success.light}` : `2px solid ${theme.palette.grey[400]}`,\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      transform: 'scale(1.1)',\n      boxShadow: theme.shadows[4]\n    }\n  };\n});\n_c = BadgeAvatar;\nconst BadgeRewardsCard = _ref2 => {\n  let {\n    availableBadges = [],\n    earnedBadges = [],\n    onBadgeClick\n  } = _ref2;\n  if (availableBadges.length === 0) return null;\n  const handleBadgeClick = badge => {\n    if (onBadgeClick) {\n      onBadgeClick(badge);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: \"badge_rewards_showcase\",\n    type: \"progress\",\n    title: \"\\uD83C\\uDFC6 Your Badge Progress\",\n    description: \"Complete tasks to earn badges and show off your achievements!\",\n    resetAfterDays: 7,\n    animation: \"slide\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      mt: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: availableBadges.slice(0, 4).map((badge, index) => {\n          const isEarned = earnedBadges.includes(badge.id);\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              onClick: () => handleBadgeClick(badge),\n              children: [/*#__PURE__*/_jsxDEV(BadgeAvatar, {\n                earned: isEarned,\n                children: badge.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                display: \"block\",\n                mt: 1,\n                color: isEarned ? 'success.main' : 'text.secondary',\n                fontWeight: isEarned ? 'bold' : 'normal',\n                children: badge.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                display: \"block\",\n                color: \"text.secondary\",\n                sx: {\n                  fontSize: '0.7rem'\n                },\n                children: badge.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)\n          }, badge.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), availableBadges.length > 4 && /*#__PURE__*/_jsxDEV(Box, {\n        mt: 2,\n        textAlign: \"center\",\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"+\", availableBadges.length - 4, \" more badges available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c2 = BadgeRewardsCard;\nexport default BadgeRewardsCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"BadgeAvatar\");\n$RefreshReg$(_c2, \"BadgeRewardsCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Avatar", "Grid", "styled", "UXCard", "jsxDEV", "_jsxDEV", "BadgeAvatar", "_ref", "theme", "earned", "width", "height", "fontSize", "backgroundColor", "palette", "success", "main", "grey", "color", "border", "light", "cursor", "transition", "transform", "boxShadow", "shadows", "_c", "BadgeRewardsCard", "_ref2", "availableBadges", "earnedBadges", "onBadgeClick", "length", "handleBadgeClick", "badge", "id", "type", "title", "description", "resetAfterDays", "animation", "children", "mt", "container", "spacing", "slice", "map", "index", "isEarned", "includes", "item", "xs", "sm", "textAlign", "onClick", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "display", "fontWeight", "name", "sx", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/BadgeRewardsCard.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Avatar, Grid } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport UXCard from './UXCard';\n\nconst BadgeAvatar = styled(Avatar)(({ theme, earned }) => ({\n  width: 48,\n  height: 48,\n  fontSize: '1.5rem',\n  backgroundColor: earned ? theme.palette.success.main : theme.palette.grey[300],\n  color: earned ? 'white' : theme.palette.grey[600],\n  border: earned ? `2px solid ${theme.palette.success.light}` : `2px solid ${theme.palette.grey[400]}`,\n  cursor: 'pointer',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'scale(1.1)',\n    boxShadow: theme.shadows[4],\n  }\n}));\n\nconst BadgeRewardsCard = ({ availableBadges = [], earnedBadges = [], onBadgeClick }) => {\n  if (availableBadges.length === 0) return null;\n\n  const handleBadgeClick = (badge) => {\n    if (onBadgeClick) {\n      onBadgeClick(badge);\n    }\n  };\n\n  return (\n    <UXCard\n      id=\"badge_rewards_showcase\"\n      type=\"progress\"\n      title=\"🏆 Your Badge Progress\"\n      description=\"Complete tasks to earn badges and show off your achievements!\"\n      resetAfterDays={7}\n      animation=\"slide\"\n    >\n      <Box mt={2}>\n        <Grid container spacing={2}>\n          {availableBadges.slice(0, 4).map((badge, index) => {\n            const isEarned = earnedBadges.includes(badge.id);\n            return (\n              <Grid item xs={6} sm={3} key={badge.id}>\n                <Box \n                  textAlign=\"center\"\n                  onClick={() => handleBadgeClick(badge)}\n                >\n                  <BadgeAvatar earned={isEarned}>\n                    {badge.icon}\n                  </BadgeAvatar>\n                  <Typography \n                    variant=\"caption\" \n                    display=\"block\" \n                    mt={1}\n                    color={isEarned ? 'success.main' : 'text.secondary'}\n                    fontWeight={isEarned ? 'bold' : 'normal'}\n                  >\n                    {badge.name}\n                  </Typography>\n                  <Typography \n                    variant=\"caption\" \n                    display=\"block\"\n                    color=\"text.secondary\"\n                    sx={{ fontSize: '0.7rem' }}\n                  >\n                    {badge.description}\n                  </Typography>\n                </Box>\n              </Grid>\n            );\n          })}\n        </Grid>\n        \n        {availableBadges.length > 4 && (\n          <Box mt={2} textAlign=\"center\">\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              +{availableBadges.length - 4} more badges available\n            </Typography>\n          </Box>\n        )}\n      </Box>\n    </UXCard>\n  );\n};\n\nexport default BadgeRewardsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AAC7D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,WAAW,GAAGJ,MAAM,CAACF,MAAM,CAAC,CAACO,IAAA;EAAA,IAAC;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAAF,IAAA;EAAA,OAAM;IACzDG,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,QAAQ;IAClBC,eAAe,EAAEJ,MAAM,GAAGD,KAAK,CAACM,OAAO,CAACC,OAAO,CAACC,IAAI,GAAGR,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;IAC9EC,KAAK,EAAET,MAAM,GAAG,OAAO,GAAGD,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;IACjDE,MAAM,EAAEV,MAAM,GAAG,aAAaD,KAAK,CAACM,OAAO,CAACC,OAAO,CAACK,KAAK,EAAE,GAAG,aAAaZ,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE;IACpGI,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAEhB,KAAK,CAACiB,OAAO,CAAC,CAAC;IAC5B;EACF,CAAC;AAAA,CAAC,CAAC;AAACC,EAAA,GAbEpB,WAAW;AAejB,MAAMqB,gBAAgB,GAAGC,KAAA,IAA+D;EAAA,IAA9D;IAAEC,eAAe,GAAG,EAAE;IAAEC,YAAY,GAAG,EAAE;IAAEC;EAAa,CAAC,GAAAH,KAAA;EACjF,IAAIC,eAAe,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE7C,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,IAAIH,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACE7B,OAAA,CAACF,MAAM;IACLgC,EAAE,EAAC,wBAAwB;IAC3BC,IAAI,EAAC,UAAU;IACfC,KAAK,EAAC,kCAAwB;IAC9BC,WAAW,EAAC,+DAA+D;IAC3EC,cAAc,EAAE,CAAE;IAClBC,SAAS,EAAC,OAAO;IAAAC,QAAA,eAEjBpC,OAAA,CAACP,GAAG;MAAC4C,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACTpC,OAAA,CAACJ,IAAI;QAAC0C,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAH,QAAA,EACxBZ,eAAe,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACZ,KAAK,EAAEa,KAAK,KAAK;UACjD,MAAMC,QAAQ,GAAGlB,YAAY,CAACmB,QAAQ,CAACf,KAAK,CAACC,EAAE,CAAC;UAChD,oBACE9B,OAAA,CAACJ,IAAI;YAACiD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACtBpC,OAAA,CAACP,GAAG;cACFuD,SAAS,EAAC,QAAQ;cAClBC,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAACC,KAAK,CAAE;cAAAO,QAAA,gBAEvCpC,OAAA,CAACC,WAAW;gBAACG,MAAM,EAAEuC,QAAS;gBAAAP,QAAA,EAC3BP,KAAK,CAACqB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACdtD,OAAA,CAACN,UAAU;gBACT6D,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAC,OAAO;gBACfnB,EAAE,EAAE,CAAE;gBACNxB,KAAK,EAAE8B,QAAQ,GAAG,cAAc,GAAG,gBAAiB;gBACpDc,UAAU,EAAEd,QAAQ,GAAG,MAAM,GAAG,QAAS;gBAAAP,QAAA,EAExCP,KAAK,CAAC6B;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACbtD,OAAA,CAACN,UAAU;gBACT6D,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAC,OAAO;gBACf3C,KAAK,EAAC,gBAAgB;gBACtB8C,EAAE,EAAE;kBAAEpD,QAAQ,EAAE;gBAAS,CAAE;gBAAA6B,QAAA,EAE1BP,KAAK,CAACI;cAAW;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAzBsBzB,KAAK,CAACC,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BhC,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEN9B,eAAe,CAACG,MAAM,GAAG,CAAC,iBACzB3B,OAAA,CAACP,GAAG;QAAC4C,EAAE,EAAE,CAAE;QAACW,SAAS,EAAC,QAAQ;QAAAZ,QAAA,eAC5BpC,OAAA,CAACN,UAAU;UAAC6D,OAAO,EAAC,SAAS;UAAC1C,KAAK,EAAC,gBAAgB;UAAAuB,QAAA,GAAC,GAClD,EAACZ,eAAe,CAACG,MAAM,GAAG,CAAC,EAAC,wBAC/B;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACM,GAAA,GAhEItC,gBAAgB;AAkEtB,eAAeA,gBAAgB;AAAC,IAAAD,EAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAAxC,EAAA;AAAAwC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}