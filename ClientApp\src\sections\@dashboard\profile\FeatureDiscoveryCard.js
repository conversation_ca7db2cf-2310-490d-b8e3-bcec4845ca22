import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  AvatarGroup,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Explore as ExploreIcon,
  QrCode as QrCodeIcon,
  Analytics as AnalyticsIcon,
  Share as ShareIcon,
  Star as StarIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function FeatureDiscoveryCard({ userCategory, onFeatureExplore }) {
  const [isVisible, setIsVisible] = useState(false);
  const [currentFeature, setCurrentFeature] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('featureDiscoveryVisible');
    const lastShown = localStorage.getItem('featureDiscoveryLastShown');
    const now = new Date().getTime();
    const daysSinceLastShown = lastShown ? (now - parseInt(lastShown)) / (1000 * 60 * 60 * 24) : 999;
    
    // Show if not dismissed and either first time or 7 days have passed
    setIsVisible(isCardVisible !== 'false' && daysSinceLastShown > 7);
    
    // Cycle through features every 5 seconds
    const interval = setInterval(() => {
      setCurrentFeature(prev => (prev + 1) % features.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('featureDiscoveryVisible', 'false');
    localStorage.setItem('featureDiscoveryLastShown', new Date().getTime().toString());
  };

  const features = [
    {
      icon: <QrCodeIcon sx={{ color: '#9C27B0' }} />,
      title: 'QR Code Sharing',
      description: 'Generate a QR code for instant profile sharing at events',
      action: 'Try QR Code',
      color: '#9C27B0',
      category: ['Free', 'Student', 'Professional', 'Business'],
    },
    {
      icon: <AnalyticsIcon sx={{ color: '#FF5722' }} />,
      title: 'Advanced Analytics',
      description: 'Track profile views, link clicks, and engagement metrics',
      action: 'View Analytics',
      color: '#FF5722',
      category: ['Professional', 'Business'],
    },
    {
      icon: <ShareIcon sx={{ color: '#4CAF50' }} />,
      title: 'Custom Domains',
      description: 'Use your own domain for professional branding',
      action: 'Upgrade Now',
      color: '#4CAF50',
      category: ['Business'],
    },
    {
      icon: <StarIcon sx={{ color: '#FFC107' }} />,
      title: 'Profile Rating',
      description: 'Build credibility with peer ratings and reviews',
      action: 'Learn More',
      color: '#FFC107',
      category: ['Professional', 'Business'],
    },
  ];

  // Filter features based on user category
  const availableFeatures = features.filter(feature => 
    feature.category.includes(userCategory || 'Free')
  );

  const currentFeatureData = availableFeatures[currentFeature % availableFeatures.length];

  if (!isVisible || !currentFeatureData) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          position: 'relative',
          background: `linear-gradient(135deg, ${currentFeatureData.color}15 0%, ${currentFeatureData.color}05 100%)`,
          border: `1px solid ${currentFeatureData.color}30`,
          borderRadius: 3,
          overflow: 'hidden',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
            zIndex: 2,
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent sx={{ position: 'relative' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ExploreIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="div">
              Discover New Features
            </Typography>
            <Chip
              label="New"
              size="small"
              color="primary"
              sx={{ ml: 1, fontSize: '10px', height: 20 }}
            />
          </Box>

          <motion.div
            key={currentFeature}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
              <Avatar
                sx={{
                  backgroundColor: `${currentFeatureData.color}20`,
                  mr: 2,
                  width: 48,
                  height: 48,
                }}
              >
                {currentFeatureData.icon}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {currentFeatureData.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {currentFeatureData.description}
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  endIcon={<ArrowForwardIcon />}
                  sx={{
                    backgroundColor: currentFeatureData.color,
                    '&:hover': { backgroundColor: `${currentFeatureData.color}CC` },
                    borderRadius: 2,
                  }}
                  onClick={() => {
                    if (onFeatureExplore) onFeatureExplore(currentFeatureData.title);
                    // Could navigate to specific feature or open relevant dialog
                  }}
                >
                  {currentFeatureData.action}
                </Button>
              </Box>
            </Box>
          </motion.div>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Feature {currentFeature + 1} of {availableFeatures.length}
              </Typography>
              <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 16, height: 16, fontSize: '10px' } }}>
                {availableFeatures.map((feature, index) => (
                  <Avatar
                    key={index}
                    sx={{
                      backgroundColor: index === currentFeature ? feature.color : 'grey.300',
                      width: 8,
                      height: 8,
                    }}
                  />
                ))}
              </AvatarGroup>
            </Box>
            <Typography variant="caption" color="text.secondary">
              Auto-cycling every 5s
            </Typography>
          </Box>

          {/* Progress indicator */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              height: 3,
              backgroundColor: currentFeatureData.color,
              borderRadius: '0 0 12px 12px',
              animation: 'progress 5s linear infinite',
              '@keyframes progress': {
                '0%': { width: '0%' },
                '100%': { width: '100%' },
              },
            }}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
}
