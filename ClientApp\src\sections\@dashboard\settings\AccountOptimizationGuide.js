import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  <PERSON>ne as TuneIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Analytics as AnalyticsIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function AccountOptimizationGuide({ userSettings, onOptimizeStep }) {
  const [isVisible, setIsVisible] = useState(false);
  const [completionScore, setCompletionScore] = useState(0);
  const [activeStep, setActiveStep] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('accountOptimizationGuideVisible');
    const hasIncompleteSettings = calculateCompletionScore() < 80;
    
    setIsVisible(isCardVisible !== 'false' && hasIncompleteSettings);
  }, [userSettings]);

  const calculateCompletionScore = () => {
    const optimizationSteps = getOptimizationSteps();
    const completedSteps = optimizationSteps.filter(step => step.completed).length;
    const score = (completedSteps / optimizationSteps.length) * 100;
    setCompletionScore(Math.round(score));
    return score;
  };

  const getOptimizationSteps = () => {
    // Mock user settings - replace with real user data
    const mockSettings = {
      profilePicture: true,
      bio: false,
      twoFactorAuth: false,
      emailNotifications: true,
      customTheme: false,
      analyticsEnabled: true,
      socialLinks: 2, // number of connected social accounts
    };

    return [
      {
        id: 'profile',
        title: 'Complete Your Profile',
        description: 'Add profile picture and bio for better engagement',
        icon: <PersonIcon sx={{ color: '#2196F3' }} />,
        completed: mockSettings.profilePicture && mockSettings.bio,
        priority: 'high',
        impact: '+40% profile views',
        tasks: [
          { name: 'Upload profile picture', done: mockSettings.profilePicture },
          { name: 'Write compelling bio', done: mockSettings.bio },
          { name: 'Add contact information', done: true },
        ],
      },
      {
        id: 'security',
        title: 'Secure Your Account',
        description: 'Enable two-factor authentication for better security',
        icon: <SecurityIcon sx={{ color: '#4CAF50' }} />,
        completed: mockSettings.twoFactorAuth,
        priority: 'high',
        impact: '99% hack prevention',
        tasks: [
          { name: 'Enable 2FA', done: mockSettings.twoFactorAuth },
          { name: 'Set strong password', done: true },
          { name: 'Review login sessions', done: false },
        ],
      },
      {
        id: 'notifications',
        title: 'Optimize Notifications',
        description: 'Configure alerts to stay informed without overwhelm',
        icon: <NotificationsIcon sx={{ color: '#FF9800' }} />,
        completed: mockSettings.emailNotifications,
        priority: 'medium',
        impact: 'Better engagement',
        tasks: [
          { name: 'Set email preferences', done: mockSettings.emailNotifications },
          { name: 'Configure push notifications', done: false },
          { name: 'Set digest frequency', done: true },
        ],
      },
      {
        id: 'customization',
        title: 'Personalize Experience',
        description: 'Customize theme and layout for your brand',
        icon: <PaletteIcon sx={{ color: '#9C27B0' }} />,
        completed: mockSettings.customTheme,
        priority: 'medium',
        impact: '+25% brand recognition',
        tasks: [
          { name: 'Choose custom theme', done: mockSettings.customTheme },
          { name: 'Set brand colors', done: false },
          { name: 'Upload custom logo', done: false },
        ],
      },
      {
        id: 'analytics',
        title: 'Enable Analytics',
        description: 'Track performance and optimize your presence',
        icon: <AnalyticsIcon sx={{ color: '#F44336' }} />,
        completed: mockSettings.analyticsEnabled,
        priority: 'low',
        impact: 'Data-driven growth',
        tasks: [
          { name: 'Enable analytics tracking', done: mockSettings.analyticsEnabled },
          { name: 'Set up goals', done: false },
          { name: 'Configure reports', done: false },
        ],
      },
    ];
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('accountOptimizationGuideVisible', 'false');
  };

  const handleStepClick = (stepIndex) => {
    setActiveStep(activeStep === stepIndex ? -1 : stepIndex);
  };

  const optimizationSteps = getOptimizationSteps();
  const nextIncompleteStep = optimizationSteps.find(step => !step.completed);

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#757575';
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <TuneIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Account Optimization Guide
            </Typography>
            <Chip
              label={`${completionScore}% complete`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: completionScore > 70 ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 152, 0, 0.3)',
                color: 'white',
              }}
            />
          </Box>

          {/* Progress Overview */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Optimization Progress</Typography>
              <Typography variant="body2">{completionScore}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={completionScore}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: completionScore > 70 ? '#4CAF50' : '#FF9800',
                  borderRadius: 4,
                },
              }}
            />
          </Box>

          {/* Next Action */}
          {nextIncompleteStep && (
            <Box sx={{ 
              backgroundColor: 'rgba(255,255,255,0.1)', 
              borderRadius: 2, 
              p: 2, 
              mb: 3,
              border: `2px solid ${getPriorityColor(nextIncompleteStep.priority)}`,
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar
                  sx={{
                    backgroundColor: `${getPriorityColor(nextIncompleteStep.priority)}20`,
                    mr: 2,
                    width: 32,
                    height: 32,
                  }}
                >
                  {nextIncompleteStep.icon}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Next: {nextIncompleteStep.title}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.9 }}>
                    {nextIncompleteStep.description}
                  </Typography>
                </Box>
                <Chip
                  label={nextIncompleteStep.impact}
                  size="small"
                  sx={{
                    backgroundColor: getPriorityColor(nextIncompleteStep.priority),
                    color: 'white',
                    fontSize: '10px',
                  }}
                />
              </Box>
              <Button
                size="small"
                onClick={() => {
                  if (onOptimizeStep) onOptimizeStep(nextIncompleteStep.id);
                }}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                  borderRadius: 2,
                }}
              >
                Optimize Now
              </Button>
            </Box>
          )}

          {/* Optimization Steps */}
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            All Optimization Steps
          </Typography>

          <Stepper activeStep={-1} orientation="vertical">
            {optimizationSteps.map((step, index) => (
              <Step key={step.id} completed={step.completed}>
                <StepLabel
                  StepIconComponent={({ active, completed }) => (
                    <Avatar
                      sx={{
                        backgroundColor: completed ? '#4CAF50' : getPriorityColor(step.priority),
                        width: 32,
                        height: 32,
                        cursor: 'pointer',
                      }}
                      onClick={() => handleStepClick(index)}
                    >
                      {completed ? (
                        <CheckCircleIcon sx={{ fontSize: 20 }} />
                      ) : (
                        React.cloneElement(step.icon, { sx: { fontSize: 20 } })
                      )}
                    </Avatar>
                  )}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle2" sx={{ color: 'white' }}>
                      {step.title}
                    </Typography>
                    <Chip
                      label={step.priority}
                      size="small"
                      sx={{
                        backgroundColor: getPriorityColor(step.priority),
                        color: 'white',
                        fontSize: '10px',
                        height: 18,
                        textTransform: 'capitalize',
                      }}
                    />
                  </Box>
                </StepLabel>
                <StepContent>
                  <Box sx={{ backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2, p: 2, mb: 1 }}>
                    <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                      {step.description}
                    </Typography>
                    
                    <List sx={{ py: 0 }}>
                      {step.tasks.map((task, taskIndex) => (
                        <ListItem key={taskIndex} sx={{ px: 0, py: 0.25 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {task.done ? (
                              <CheckCircleIcon sx={{ fontSize: 16, color: '#4CAF50' }} />
                            ) : (
                              <RadioButtonUncheckedIcon sx={{ fontSize: 16, color: 'rgba(255,255,255,0.5)' }} />
                            )}
                          </ListItemIcon>
                          <ListItemText>
                            <Typography 
                              variant="caption" 
                              sx={{ 
                                opacity: task.done ? 0.7 : 1,
                                textDecoration: task.done ? 'line-through' : 'none',
                              }}
                            >
                              {task.name}
                            </Typography>
                          </ListItemText>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <StarIcon sx={{ fontSize: 16 }} />
              <Typography variant="caption">
                {completionScore > 80 ? 'Excellent' : completionScore > 60 ? 'Good' : 'Getting started'} optimization
              </Typography>
            </Box>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                // Auto-complete next step
                if (nextIncompleteStep && onOptimizeStep) {
                  onOptimizeStep(nextIncompleteStep.id);
                }
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Quick Setup
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
