import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  IconButton,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Chip,
  Avatar,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  Close as CloseIcon,
  Lightbulb as LightbulbIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function LinkOptimizationTips({ links, onOptimizeLink }) {
  const [isVisible, setIsVisible] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [currentTipIndex, setCurrentTipIndex] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('linkOptimizationTipsVisible');
    const hasLinks = links && links.length > 0;
    
    // Show if user has links but hasn't optimized them recently
    setIsVisible(isCardVisible !== 'false' && hasLinks);
    
    // Rotate tips every 8 seconds
    const interval = setInterval(() => {
      setCurrentTipIndex(prev => (prev + 1) % optimizationTips.length);
    }, 8000);
    
    return () => clearInterval(interval);
  }, [links]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('linkOptimizationTipsVisible', 'false');
  };

  const optimizationTips = [
    {
      icon: <EditIcon sx={{ color: '#2196F3' }} />,
      title: 'Descriptive Titles',
      description: 'Use clear, action-oriented titles like "View My Portfolio" instead of just "Portfolio"',
      impact: '+25% clicks',
      color: '#2196F3',
      example: '"Download My Resume" vs "Resume"',
    },
    {
      icon: <SpeedIcon sx={{ color: '#4CAF50' }} />,
      title: 'Link Order Matters',
      description: 'Place your most important links at the top - they get 3x more clicks',
      impact: '+300% visibility',
      color: '#4CAF50',
      example: 'Portfolio → LinkedIn → Contact',
    },
    {
      icon: <VisibilityIcon sx={{ color: '#FF9800' }} />,
      title: 'Custom Icons',
      description: 'Add custom icons to make your links stand out and increase recognition',
      impact: '+40% engagement',
      color: '#FF9800',
      example: '📧 for email, 💼 for LinkedIn',
    },
    {
      icon: <TrendingUpIcon sx={{ color: '#9C27B0' }} />,
      title: 'Regular Updates',
      description: 'Keep links fresh and relevant. Remove outdated links to maintain quality',
      impact: '+15% trust',
      color: '#9C27B0',
      example: 'Update seasonal portfolios',
    },
  ];

  const currentTip = optimizationTips[currentTipIndex];

  const linkAnalysis = {
    total: links?.length || 0,
    withCustomTitles: links?.filter(link => link.title && link.title.length > 10).length || 0,
    withIcons: links?.filter(link => link.icon).length || 0,
    optimizationScore: Math.round(((links?.filter(link => 
      link.title && link.title.length > 10 && link.icon
    ).length || 0) / Math.max(links?.length || 1, 1)) * 100),
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LightbulbIcon sx={{ mr: 1, color: '#FF9800' }} />
            <Typography variant="h6" component="div">
              Link Optimization Tips
            </Typography>
            <Chip
              label={`${linkAnalysis.optimizationScore}% optimized`}
              size="small"
              color={linkAnalysis.optimizationScore > 70 ? 'success' : 'warning'}
              sx={{ ml: 1 }}
            />
          </Box>

          <motion.div
            key={currentTipIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
              <Avatar
                sx={{
                  backgroundColor: `${currentTip.color}20`,
                  mr: 2,
                  width: 40,
                  height: 40,
                }}
              >
                {currentTip.icon}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {currentTip.title}
                  </Typography>
                  <Chip
                    label={currentTip.impact}
                    size="small"
                    sx={{
                      backgroundColor: `${currentTip.color}20`,
                      color: currentTip.color,
                      fontSize: '10px',
                      height: 18,
                    }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {currentTip.description}
                </Typography>
                <Typography variant="caption" sx={{ 
                  fontStyle: 'italic', 
                  color: 'text.secondary',
                  backgroundColor: 'rgba(255,255,255,0.7)',
                  padding: '2px 6px',
                  borderRadius: 1,
                }}>
                  💡 {currentTip.example}
                </Typography>
              </Box>
            </Box>
          </motion.div>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {optimizationTips.map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: index === currentTipIndex ? currentTip.color : 'rgba(0,0,0,0.2)',
                    transition: 'all 0.3s ease',
                  }}
                />
              ))}
            </Box>
            <Typography variant="caption" color="text.secondary">
              Tip {currentTipIndex + 1} of {optimizationTips.length}
            </Typography>
          </Box>

          <Button
            variant="text"
            size="small"
            onClick={() => setExpanded(!expanded)}
            endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            sx={{ mb: 1 }}
          >
            {expanded ? 'Hide' : 'Show'} Link Analysis
          </Button>

          <Collapse in={expanded}>
            <Box sx={{ 
              backgroundColor: 'rgba(255,255,255,0.7)', 
              borderRadius: 2, 
              p: 2, 
              mb: 2 
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Your Link Performance
              </Typography>
              <List sx={{ py: 0 }}>
                <ListItem sx={{ px: 0, py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Typography variant="h6">{linkAnalysis.total}</Typography>
                  </ListItemIcon>
                  <ListItemText primary="Total Links" />
                </ListItem>
                <ListItem sx={{ px: 0, py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Typography variant="h6" color="primary">
                      {linkAnalysis.withCustomTitles}
                    </Typography>
                  </ListItemIcon>
                  <ListItemText primary="With Descriptive Titles" />
                </ListItem>
                <ListItem sx={{ px: 0, py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Typography variant="h6" color="secondary">
                      {linkAnalysis.withIcons}
                    </Typography>
                  </ListItemIcon>
                  <ListItemText primary="With Custom Icons" />
                </ListItem>
              </List>
            </Box>
          </Collapse>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Tips refresh every 8 seconds
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                if (onOptimizeLink) onOptimizeLink();
                // Could open link editing interface
              }}
              sx={{
                backgroundColor: currentTip.color,
                '&:hover': { backgroundColor: `${currentTip.color}CC` },
                borderRadius: 2,
              }}
            >
              Optimize Links
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
