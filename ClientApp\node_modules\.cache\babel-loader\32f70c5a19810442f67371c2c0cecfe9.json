{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\LinkOptimizationCard.js\";\nimport React from 'react';\nimport { Box, Typography, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';\nimport { TrendingUp as TrendingIcon, Link as LinkIcon, Visibility as VisibilityIcon, Speed as SpeedIcon } from '@mui/icons-material';\nimport UXCard from './UXCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LinkOptimizationCard = _ref => {\n  let {\n    onOptimizeTips\n  } = _ref;\n  const optimizationTips = [{\n    icon: /*#__PURE__*/_jsxDEV(TrendingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this),\n    text: 'Use action verbs like \"Watch Now\", \"Join Us\", \"Download\"',\n    tip: 'action_verbs'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this),\n    text: 'Keep URLs short and memorable',\n    tip: 'short_urls'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this),\n    text: 'Add custom images to increase click rates',\n    tip: 'custom_images'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SpeedIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    text: 'Test your links regularly to ensure they work',\n    tip: 'test_links'\n  }];\n  const handleTipClick = tip => {\n    if (onOptimizeTips) {\n      onOptimizeTips(tip);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: \"link_optimization_tips\",\n    type: \"tip\",\n    title: \"\\u2728 Make Your Links Shine\",\n    description: \"Optimize your links for maximum engagement and professional appearance.\",\n    resetAfterDays: 5,\n    animation: \"slide\",\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: optimizationTips.map((tip, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n        sx: {\n          cursor: 'pointer',\n          borderRadius: 1,\n          '&:hover': {\n            backgroundColor: 'rgba(0,0,0,0.04)'\n          }\n        },\n        onClick: () => handleTipClick(tip.tip),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          sx: {\n            color: 'primary.main',\n            minWidth: 36\n          },\n          children: tip.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: tip.text,\n          primaryTypographyProps: {\n            variant: 'body2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_c = LinkOptimizationCard;\nexport default LinkOptimizationCard;\nvar _c;\n$RefreshReg$(_c, \"LinkOptimizationCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "List", "ListItem", "ListItemIcon", "ListItemText", "TrendingUp", "TrendingIcon", "Link", "LinkIcon", "Visibility", "VisibilityIcon", "Speed", "SpeedIcon", "UXCard", "jsxDEV", "_jsxDEV", "LinkOptimizationCard", "_ref", "onOptimizeTips", "optimizationTips", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "tip", "handleTipClick", "id", "type", "title", "description", "resetAfterDays", "animation", "children", "dense", "map", "index", "sx", "cursor", "borderRadius", "backgroundColor", "onClick", "color", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/LinkOptimizationCard.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';\nimport { \n  TrendingUp as TrendingIcon,\n  Link as LinkIcon,\n  Visibility as VisibilityIcon,\n  Speed as SpeedIcon \n} from '@mui/icons-material';\nimport UXCard from './UXCard';\n\nconst LinkOptimizationCard = ({ onOptimizeTips }) => {\n  const optimizationTips = [\n    {\n      icon: <TrendingIcon />,\n      text: 'Use action verbs like \"Watch Now\", \"Join Us\", \"Download\"',\n      tip: 'action_verbs'\n    },\n    {\n      icon: <LinkIcon />,\n      text: 'Keep URLs short and memorable',\n      tip: 'short_urls'\n    },\n    {\n      icon: <VisibilityIcon />,\n      text: 'Add custom images to increase click rates',\n      tip: 'custom_images'\n    },\n    {\n      icon: <SpeedIcon />,\n      text: 'Test your links regularly to ensure they work',\n      tip: 'test_links'\n    }\n  ];\n\n  const handleTipClick = (tip) => {\n    if (onOptimizeTips) {\n      onOptimizeTips(tip);\n    }\n  };\n\n  return (\n    <UXCard\n      id=\"link_optimization_tips\"\n      type=\"tip\"\n      title=\"✨ Make Your Links Shine\"\n      description=\"Optimize your links for maximum engagement and professional appearance.\"\n      resetAfterDays={5}\n      animation=\"slide\"\n    >\n      <List dense>\n        {optimizationTips.map((tip, index) => (\n          <ListItem \n            key={index}\n            sx={{ \n              cursor: 'pointer',\n              borderRadius: 1,\n              '&:hover': { \n                backgroundColor: 'rgba(0,0,0,0.04)' \n              }\n            }}\n            onClick={() => handleTipClick(tip.tip)}\n          >\n            <ListItemIcon sx={{ color: 'primary.main', minWidth: 36 }}>\n              {tip.icon}\n            </ListItemIcon>\n            <ListItemText \n              primary={tip.text}\n              primaryTypographyProps={{ variant: 'body2' }}\n            />\n          </ListItem>\n        ))}\n      </List>\n    </UXCard>\n  );\n};\n\nexport default LinkOptimizationCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,QAAQ,eAAe;AAC3F,SACEC,UAAU,IAAIC,YAAY,EAC1BC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,oBAAoB,GAAGC,IAAA,IAAwB;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAD,IAAA;EAC9C,MAAME,gBAAgB,GAAG,CACvB;IACEC,IAAI,eAAEL,OAAA,CAACT,YAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE,0DAA0D;IAChEC,GAAG,EAAE;EACP,CAAC,EACD;IACEN,IAAI,eAAEL,OAAA,CAACP,QAAQ;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,+BAA+B;IACrCC,GAAG,EAAE;EACP,CAAC,EACD;IACEN,IAAI,eAAEL,OAAA,CAACL,cAAc;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,IAAI,EAAE,2CAA2C;IACjDC,GAAG,EAAE;EACP,CAAC,EACD;IACEN,IAAI,eAAEL,OAAA,CAACH,SAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE,+CAA+C;IACrDC,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,cAAc,GAAID,GAAG,IAAK;IAC9B,IAAIR,cAAc,EAAE;MAClBA,cAAc,CAACQ,GAAG,CAAC;IACrB;EACF,CAAC;EAED,oBACEX,OAAA,CAACF,MAAM;IACLe,EAAE,EAAC,wBAAwB;IAC3BC,IAAI,EAAC,KAAK;IACVC,KAAK,EAAC,8BAAyB;IAC/BC,WAAW,EAAC,yEAAyE;IACrFC,cAAc,EAAE,CAAE;IAClBC,SAAS,EAAC,OAAO;IAAAC,QAAA,eAEjBnB,OAAA,CAACd,IAAI;MAACkC,KAAK;MAAAD,QAAA,EACRf,gBAAgB,CAACiB,GAAG,CAAC,CAACV,GAAG,EAAEW,KAAK,kBAC/BtB,OAAA,CAACb,QAAQ;QAEPoC,EAAE,EAAE;UACFC,MAAM,EAAE,SAAS;UACjBC,YAAY,EAAE,CAAC;UACf,SAAS,EAAE;YACTC,eAAe,EAAE;UACnB;QACF,CAAE;QACFC,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACD,GAAG,CAACA,GAAG,CAAE;QAAAQ,QAAA,gBAEvCnB,OAAA,CAACZ,YAAY;UAACmC,EAAE,EAAE;YAAEK,KAAK,EAAE,cAAc;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAV,QAAA,EACvDR,GAAG,CAACN;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACfT,OAAA,CAACX,YAAY;UACXyC,OAAO,EAAEnB,GAAG,CAACD,IAAK;UAClBqB,sBAAsB,EAAE;YAAEC,OAAO,EAAE;UAAQ;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA,GAhBGa,KAAK;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBF,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAACwB,EAAA,GAhEIhC,oBAAoB;AAkE1B,eAAeA,oBAAoB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}