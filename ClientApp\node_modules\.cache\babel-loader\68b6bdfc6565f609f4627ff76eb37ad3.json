{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\components\\\\UXEnhancements\\\\DailyTipCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button } from '@mui/material';\nimport { Lightbulb as LightbulbIcon, ArrowForward as ArrowIcon } from '@mui/icons-material';\nimport UXCard from './UXCard';\nimport useUXEnhancements from '../../hooks/useUXEnhancements';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DailyTipCard = _ref => {\n  _s();\n  let {\n    onLearnMore\n  } = _ref;\n  const {\n    dailyTip\n  } = useUXEnhancements();\n  if (!dailyTip) return null;\n  const handleLearnMore = () => {\n    if (onLearnMore) {\n      onLearnMore(dailyTip.id);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UXCard, {\n    id: dailyTip.id,\n    type: dailyTip.type,\n    title: `💡 ${dailyTip.title}`,\n    description: dailyTip.description,\n    actionText: \"Learn More\",\n    onAction: handleLearnMore,\n    resetAfterDays: 1 // Reset daily\n    ,\n    animation: \"fade\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      mt: 1,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: \"\\uD83D\\uDCAB Daily tip \\u2022 Resets tomorrow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(DailyTipCard, \"BN1mKRuUn5Dq39bT6w5uLFNbDiE=\", false, function () {\n  return [useUXEnhancements];\n});\n_c = DailyTipCard;\nexport default DailyTipCard;\nvar _c;\n$RefreshReg$(_c, \"DailyTipCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Lightbulb", "LightbulbIcon", "ArrowForward", "ArrowIcon", "UXCard", "useUXEnhancements", "jsxDEV", "_jsxDEV", "DailyTipCard", "_ref", "_s", "onLearnMore", "dailyTip", "handleLearnMore", "id", "type", "title", "description", "actionText", "onAction", "resetAfterDays", "animation", "children", "mt", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/components/UXEnhancements/DailyTipCard.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Button } from '@mui/material';\nimport { \n  Lightbulb as LightbulbIcon,\n  ArrowForward as ArrowIcon \n} from '@mui/icons-material';\nimport UXCard from './UXCard';\nimport useUXEnhancements from '../../hooks/useUXEnhancements';\n\nconst DailyTipCard = ({ onLearnMore }) => {\n  const { dailyTip } = useUXEnhancements();\n  \n  if (!dailyTip) return null;\n\n  const handleLearnMore = () => {\n    if (onLearnMore) {\n      onLearnMore(dailyTip.id);\n    }\n  };\n\n  return (\n    <UXCard\n      id={dailyTip.id}\n      type={dailyTip.type}\n      title={`💡 ${dailyTip.title}`}\n      description={dailyTip.description}\n      actionText=\"Learn More\"\n      onAction={handleLearnMore}\n      resetAfterDays={1} // Reset daily\n      animation=\"fade\"\n    >\n      <Box mt={1}>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          💫 Daily tip • Resets tomorrow\n        </Typography>\n      </Box>\n    </UXCard>\n  );\n};\n\nexport default DailyTipCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACvD,SACEC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,SAAS,QACpB,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,iBAAiB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAGC,IAAA,IAAqB;EAAAC,EAAA;EAAA,IAApB;IAAEC;EAAY,CAAC,GAAAF,IAAA;EACnC,MAAM;IAAEG;EAAS,CAAC,GAAGP,iBAAiB,CAAC,CAAC;EAExC,IAAI,CAACO,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIF,WAAW,EAAE;MACfA,WAAW,CAACC,QAAQ,CAACE,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,oBACEP,OAAA,CAACH,MAAM;IACLU,EAAE,EAAEF,QAAQ,CAACE,EAAG;IAChBC,IAAI,EAAEH,QAAQ,CAACG,IAAK;IACpBC,KAAK,EAAE,MAAMJ,QAAQ,CAACI,KAAK,EAAG;IAC9BC,WAAW,EAAEL,QAAQ,CAACK,WAAY;IAClCC,UAAU,EAAC,YAAY;IACvBC,QAAQ,EAAEN,eAAgB;IAC1BO,cAAc,EAAE,CAAE,CAAC;IAAA;IACnBC,SAAS,EAAC,MAAM;IAAAC,QAAA,eAEhBf,OAAA,CAACV,GAAG;MAAC0B,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTf,OAAA,CAACT,UAAU;QAAC0B,OAAO,EAAC,SAAS;QAACC,KAAK,EAAC,gBAAgB;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACnB,EAAA,CA7BIF,YAAY;EAAA,QACKH,iBAAiB;AAAA;AAAAyB,EAAA,GADlCtB,YAAY;AA+BlB,eAAeA,YAAY;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}