{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDigics\\\\ClientApp\\\\src\\\\layouts\\\\dashboard\\\\DashboardLayout.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Outlet } from \"react-router-dom\";\n// @mui\nimport { styled } from \"@mui/material/styles\";\n//\nimport Header from \"./header\";\nimport Nav from \"./nav\";\n// UX Enhancement Components\nimport { DidYouKnowWidget, GoalTrackerWidget } from \"../../components/UXEnhancements\";\n\n// ----------------------------------------------------------------------\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst APP_BAR_MOBILE = 64;\nconst APP_BAR_DESKTOP = 92;\nconst StyledRoot = styled(\"div\")({\n  display: \"flex\",\n  minHeight: \"100%\",\n  overflow: \"hidden\"\n});\n_c = StyledRoot;\nconst Main = styled(\"div\")(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    flexGrow: 1,\n    overflow: \"auto\",\n    minHeight: \"100%\",\n    paddingTop: APP_BAR_MOBILE + 24,\n    paddingBottom: theme.spacing(10),\n    [theme.breakpoints.up(\"lg\")]: {\n      paddingTop: APP_BAR_DESKTOP + 24,\n      paddingLeft: theme.spacing(2),\n      paddingRight: theme.spacing(2)\n    }\n  };\n});\n\n// ----------------------------------------------------------------------\n_c2 = Main;\nexport default function DashboardLayout() {\n  _s();\n  const [open, setOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(StyledRoot, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      onOpenNav: () => setOpen(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {\n      openNav: open,\n      onCloseNav: () => setOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Main, {\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardLayout, \"xG1TONbKtDWtdOTrXaTAsNhPg/Q=\");\n_c3 = DashboardLayout;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"StyledRoot\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"DashboardLayout\");", "map": {"version": 3, "names": ["useState", "Outlet", "styled", "Header", "Nav", "DidYouKnowWidget", "GoalTrackerWidget", "jsxDEV", "_jsxDEV", "APP_BAR_MOBILE", "APP_BAR_DESKTOP", "StyledRoot", "display", "minHeight", "overflow", "_c", "Main", "_ref", "theme", "flexGrow", "paddingTop", "paddingBottom", "spacing", "breakpoints", "up", "paddingLeft", "paddingRight", "_c2", "DashboardLayout", "_s", "open", "<PERSON><PERSON><PERSON>", "children", "onOpenNav", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "openNav", "onCloseNav", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/IDigics/ClientApp/src/layouts/dashboard/DashboardLayout.js"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Outlet } from \"react-router-dom\";\r\n// @mui\r\nimport { styled } from \"@mui/material/styles\";\r\n//\r\nimport Header from \"./header\";\r\nimport Nav from \"./nav\";\r\n// UX Enhancement Components\r\nimport {\r\n  DidYouKnowWidget,\r\n  GoalTrackerWidget\r\n} from \"../../components/UXEnhancements\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst APP_BAR_MOBILE = 64;\r\nconst APP_BAR_DESKTOP = 92;\r\n\r\nconst StyledRoot = styled(\"div\")({\r\n  display: \"flex\",\r\n  minHeight: \"100%\",\r\n  overflow: \"hidden\",\r\n});\r\n\r\nconst Main = styled(\"div\")(({ theme }) => ({\r\n  flexGrow: 1,\r\n  overflow: \"auto\",\r\n  minHeight: \"100%\",\r\n  paddingTop: APP_BAR_MOBILE + 24,\r\n  paddingBottom: theme.spacing(10),\r\n  [theme.breakpoints.up(\"lg\")]: {\r\n    paddingTop: APP_BAR_DESKTOP + 24,\r\n    paddingLeft: theme.spacing(2),\r\n    paddingRight: theme.spacing(2),\r\n  },\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function DashboardLayout() {\r\n  const [open, setOpen] = useState(false);\r\n\r\n  return (\r\n    <StyledRoot>\r\n      <Header onOpenNav={() => setOpen(true)} />\r\n\r\n      <Nav openNav={open} onCloseNav={() => setOpen(false)} />\r\n\r\n      <Main>\r\n        <Outlet />\r\n      </Main>\r\n\r\n      {/* <footer className=\"footer bg-white\">\r\n                <p\r\n                    className=\"trademark text-secondary\"\r\n                    style={{ backgroundColor: \"transparent\" }}\r\n                >\r\n                    <span className=\"logo\">IDigics &trade;</span>\r\n                </p>\r\n                <a\r\n                    className=\"logo-link\"\r\n                    href=\"/\"\r\n                    rel=\"noopener noreferrer\"\r\n                    target=\"_blank\"\r\n                >\r\n                    <span className=\"logo\">IDigics</span>\r\n                </a>\r\n            </footer> */}\r\n    </StyledRoot>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,kBAAkB;AACzC;AACA,SAASC,MAAM,QAAQ,sBAAsB;AAC7C;AACA,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,GAAG,MAAM,OAAO;AACvB;AACA,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,iCAAiC;;AAExC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,eAAe,GAAG,EAAE;AAE1B,MAAMC,UAAU,GAAGT,MAAM,CAAC,KAAK,CAAC,CAAC;EAC/BU,OAAO,EAAE,MAAM;EACfC,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AAACC,EAAA,GAJGJ,UAAU;AAMhB,MAAMK,IAAI,GAAGd,MAAM,CAAC,KAAK,CAAC,CAACe,IAAA;EAAA,IAAC;IAAEC;EAAM,CAAC,GAAAD,IAAA;EAAA,OAAM;IACzCE,QAAQ,EAAE,CAAC;IACXL,QAAQ,EAAE,MAAM;IAChBD,SAAS,EAAE,MAAM;IACjBO,UAAU,EAAEX,cAAc,GAAG,EAAE;IAC/BY,aAAa,EAAEH,KAAK,CAACI,OAAO,CAAC,EAAE,CAAC;IAChC,CAACJ,KAAK,CAACK,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5BJ,UAAU,EAAEV,eAAe,GAAG,EAAE;MAChCe,WAAW,EAAEP,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;MAC7BI,YAAY,EAAER,KAAK,CAACI,OAAO,CAAC,CAAC;IAC/B;EACF,CAAC;AAAA,CAAC,CAAC;;AAEH;AAAAK,GAAA,GAbMX,IAAI;AAeV,eAAe,SAASY,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEvC,oBACEQ,OAAA,CAACG,UAAU;IAAAqB,QAAA,gBACTxB,OAAA,CAACL,MAAM;MAAC8B,SAAS,EAAEA,CAAA,KAAMF,OAAO,CAAC,IAAI;IAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1C7B,OAAA,CAACJ,GAAG;MAACkC,OAAO,EAAER,IAAK;MAACS,UAAU,EAAEA,CAAA,KAAMR,OAAO,CAAC,KAAK;IAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExD7B,OAAA,CAACQ,IAAI;MAAAgB,QAAA,eACHxB,OAAA,CAACP,MAAM;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAkBG,CAAC;AAEjB;AAACR,EAAA,CA/BuBD,eAAe;AAAAY,GAAA,GAAfZ,eAAe;AAAA,IAAAb,EAAA,EAAAY,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}