import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Compare as CompareIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  Analytics as AnalyticsIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function FeatureComparisonWidget({ currentPlan, onSelectPlan }) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCategory, setActiveCategory] = useState(0);
  const [highlightedPlan, setHighlightedPlan] = useState('Pro');

  useEffect(() => {
    const isCardVisible = localStorage.getItem('featureComparisonWidgetVisible');
    const isFreePlan = currentPlan === 'Free' || currentPlan === 'Student';
    
    setIsVisible(isCardVisible !== 'false' && isFreePlan);
  }, [currentPlan]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('featureComparisonWidgetVisible', 'false');
  };

  const featureCategories = [
    {
      name: 'Core Features',
      icon: <StarIcon sx={{ fontSize: 16 }} />,
      features: [
        {
          name: 'Profile Links',
          free: '5 links',
          pro: 'Unlimited',
          business: 'Unlimited',
          tooltip: 'Number of links you can add to your profile',
        },
        {
          name: 'Link Clicks',
          free: 'Basic tracking',
          pro: 'Advanced analytics',
          business: 'Premium insights',
          tooltip: 'Level of click tracking and analytics available',
        },
        {
          name: 'Profile Views',
          free: 'Limited data',
          pro: 'Detailed metrics',
          business: 'Real-time data',
          tooltip: 'Depth of profile view analytics',
        },
        {
          name: 'Custom Themes',
          free: '3 themes',
          pro: '20+ themes',
          business: 'Unlimited + Custom',
          tooltip: 'Number of visual themes available',
        },
      ],
    },
    {
      name: 'Branding',
      icon: <PaletteIcon sx={{ fontSize: 16 }} />,
      features: [
        {
          name: 'Custom Domain',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Use your own domain name (yourname.com)',
        },
        {
          name: 'Remove IDigics Branding',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Remove "Powered by IDigics" from your profile',
        },
        {
          name: 'Custom Logo',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Add your own logo to your profile',
        },
        {
          name: 'White-label Solution',
          free: false,
          pro: false,
          business: true,
          tooltip: 'Complete white-label branding solution',
        },
      ],
    },
    {
      name: 'Analytics',
      icon: <AnalyticsIcon sx={{ fontSize: 16 }} />,
      features: [
        {
          name: 'Geographic Data',
          free: false,
          pro: true,
          business: true,
          tooltip: 'See where your visitors are coming from',
        },
        {
          name: 'Time-based Analytics',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Understand when your audience is most active',
        },
        {
          name: 'Conversion Tracking',
          free: false,
          pro: 'Basic',
          business: 'Advanced',
          tooltip: 'Track how visitors convert through your links',
        },
        {
          name: 'Export Data',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Export your analytics data to CSV/Excel',
        },
      ],
    },
    {
      name: 'Support',
      icon: <SecurityIcon sx={{ fontSize: 16 }} />,
      features: [
        {
          name: 'Email Support',
          free: true,
          pro: true,
          business: true,
          tooltip: 'Get help via email support',
        },
        {
          name: 'Priority Support',
          free: false,
          pro: true,
          business: true,
          tooltip: 'Get faster response times',
        },
        {
          name: 'Live Chat',
          free: false,
          pro: false,
          business: true,
          tooltip: 'Real-time chat support',
        },
        {
          name: 'Dedicated Manager',
          free: false,
          pro: false,
          business: true,
          tooltip: 'Personal account manager',
        },
      ],
    },
  ];

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      color: '#757575',
      popular: false,
    },
    {
      name: 'Pro',
      price: '$9',
      period: 'month',
      color: '#2196F3',
      popular: true,
    },
    {
      name: 'Business',
      price: '$29',
      period: 'month',
      color: '#9C27B0',
      popular: false,
    },
  ];

  const renderFeatureValue = (feature, plan) => {
    const value = feature[plan.toLowerCase()];
    
    if (typeof value === 'boolean') {
      return value ? (
        <CheckCircleIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
      ) : (
        <CancelIcon sx={{ color: '#F44336', fontSize: 20 }} />
      );
    }
    
    return (
      <Typography variant="body2" sx={{ fontWeight: plan === highlightedPlan ? 600 : 400 }}>
        {value}
      </Typography>
    );
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <CompareIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Compare Plans & Features
            </Typography>
            <Chip
              label="Choose Wisely"
              size="small"
              sx={{
                ml: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
          </Box>

          {/* Plan Headers */}
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            {plans.map((plan) => (
              <Box
                key={plan.name}
                sx={{
                  flex: 1,
                  backgroundColor: plan.name === highlightedPlan ? 'rgba(255,255,255,0.2)' : 'rgba(255,255,255,0.1)',
                  borderRadius: 2,
                  p: 2,
                  textAlign: 'center',
                  border: plan.popular ? '2px solid #FFD700' : '2px solid transparent',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                }}
                onClick={() => setHighlightedPlan(plan.name)}
              >
                {plan.popular && (
                  <Chip
                    label="Most Popular"
                    size="small"
                    sx={{
                      backgroundColor: '#FFD700',
                      color: 'black',
                      fontWeight: 600,
                      mb: 1,
                    }}
                  />
                )}
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {plan.name}
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 700, color: plan.color }}>
                  {plan.price}
                </Typography>
                <Typography variant="caption">per {plan.period}</Typography>
              </Box>
            ))}
          </Box>

          {/* Category Tabs */}
          <Tabs
            value={activeCategory}
            onChange={(e, newValue) => setActiveCategory(newValue)}
            sx={{
              mb: 2,
              '& .MuiTab-root': { 
                color: 'rgba(255,255,255,0.7)', 
                minHeight: 40,
                fontSize: '12px',
              },
              '& .Mui-selected': { color: 'white' },
              '& .MuiTabs-indicator': { backgroundColor: 'white' },
            }}
          >
            {featureCategories.map((category, index) => (
              <Tab
                key={index}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {category.icon}
                    {category.name}
                  </Box>
                }
              />
            ))}
          </Tabs>

          {/* Feature Comparison Table */}
          <Box sx={{ backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2, mb: 3 }}>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: 'white', fontWeight: 600, border: 'none' }}>
                      Feature
                    </TableCell>
                    {plans.map((plan) => (
                      <TableCell
                        key={plan.name}
                        align="center"
                        sx={{
                          color: 'white',
                          fontWeight: 600,
                          border: 'none',
                          backgroundColor: plan.name === highlightedPlan ? 'rgba(255,255,255,0.1)' : 'transparent',
                        }}
                      >
                        {plan.name}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {featureCategories[activeCategory].features.map((feature, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ color: 'white', border: 'none', py: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2">{feature.name}</Typography>
                          <Tooltip title={feature.tooltip} arrow>
                            <InfoIcon sx={{ fontSize: 14, opacity: 0.7 }} />
                          </Tooltip>
                        </Box>
                      </TableCell>
                      {plans.map((plan) => (
                        <TableCell
                          key={plan.name}
                          align="center"
                          sx={{
                            color: 'white',
                            border: 'none',
                            py: 1,
                            backgroundColor: plan.name === highlightedPlan ? 'rgba(255,255,255,0.1)' : 'transparent',
                          }}
                        >
                          {renderFeatureValue(feature, plan.name)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              Highlighting: {highlightedPlan} Plan
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                if (onSelectPlan) onSelectPlan(highlightedPlan);
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Choose {highlightedPlan}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
