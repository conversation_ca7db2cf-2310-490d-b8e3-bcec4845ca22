import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Box,
  <PERSON>ton,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Close as CloseIcon,
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  Visibility as VisibilityIcon,
  Mouse as MouseIcon,
  Schedule as ScheduleIcon,
  Public as PublicIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function AnalyticsInsightsTutorial({ hasData, onStartTour }) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  useEffect(() => {
    const isCardVisible = localStorage.getItem('analyticsInsightsTutorialVisible');
    const hasSeenTutorial = localStorage.getItem('hasSeenAnalyticsTutorial');
    
    // Show if user hasn't dismissed and either has no data or hasn't seen tutorial
    setIsVisible(isCardVisible !== 'false' && (!hasData || hasSeenTutorial !== 'true'));
  }, [hasData]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('analyticsInsightsTutorialVisible', 'false');
    localStorage.setItem('hasSeenAnalyticsTutorial', 'true');
  };

  const handleStepComplete = (stepIndex) => {
    const newCompleted = new Set(completedSteps);
    newCompleted.add(stepIndex);
    setCompletedSteps(newCompleted);
    
    if (stepIndex < tutorialSteps.length - 1) {
      setActiveStep(stepIndex + 1);
    }
  };

  const tutorialSteps = [
    {
      label: 'Understanding Views',
      icon: <VisibilityIcon sx={{ color: '#2196F3' }} />,
      content: {
        title: 'Profile Views = Visibility',
        description: 'Views show how many people discovered your profile. Higher views mean better reach.',
        tips: [
          'Share your profile link on social media',
          'Add it to your email signature',
          'Include it in your bio on other platforms',
        ],
        metric: 'Aim for 100+ views per month',
      },
    },
    {
      label: 'Analyzing Clicks',
      icon: <MouseIcon sx={{ color: '#4CAF50' }} />,
      content: {
        title: 'Clicks = Engagement',
        description: 'Clicks show which links people find most interesting. This helps you understand your audience.',
        tips: [
          'Top-performing links should stay at the top',
          'Remove or update low-performing links',
          'Test different link titles and descriptions',
        ],
        metric: 'Target 10%+ click-through rate',
      },
    },
    {
      label: 'Time Patterns',
      icon: <ScheduleIcon sx={{ color: '#FF9800' }} />,
      content: {
        title: 'Timing = Strategy',
        description: 'Peak activity times help you know when to share content and engage with your audience.',
        tips: [
          'Post new content during peak hours',
          'Schedule social media updates accordingly',
          'Plan important announcements for high-traffic times',
        ],
        metric: 'Identify your top 3 peak hours',
      },
    },
    {
      label: 'Geographic Insights',
      icon: <PublicIcon sx={{ color: '#9C27B0' }} />,
      content: {
        title: 'Location = Opportunity',
        description: 'Geographic data reveals where your audience is located, helping you tailor content and timing.',
        tips: [
          'Consider time zones for global audience',
          'Localize content for top regions',
          'Explore networking opportunities in key areas',
        ],
        metric: 'Know your top 3 countries/regions',
      },
    },
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <SchoolIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Analytics Insights Tutorial
            </Typography>
            <Chip
              label={`${completedSteps.size}/${tutorialSteps.length} completed`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
          </Box>

          <Stepper activeStep={activeStep} orientation="vertical" sx={{ mb: 3 }}>
            {tutorialSteps.map((step, index) => (
              <Step key={step.label} completed={completedSteps.has(index)}>
                <StepLabel
                  StepIconComponent={({ active, completed }) => (
                    <Avatar
                      sx={{
                        backgroundColor: completed ? '#4CAF50' : active ? 'rgba(255,255,255,0.2)' : 'rgba(255,255,255,0.1)',
                        width: 32,
                        height: 32,
                      }}
                    >
                      {completed ? (
                        <CheckCircleIcon sx={{ fontSize: 20 }} />
                      ) : (
                        React.cloneElement(step.icon, { sx: { fontSize: 20 } })
                      )}
                    </Avatar>
                  )}
                >
                  <Typography variant="subtitle2" sx={{ color: 'white' }}>
                    {step.label}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2, p: 2, mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                      {step.content.title}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                      {step.content.description}
                    </Typography>
                    
                    <Typography variant="caption" sx={{ fontWeight: 600, mb: 1, display: 'block' }}>
                      💡 Pro Tips:
                    </Typography>
                    <List sx={{ py: 0, mb: 2 }}>
                      {step.content.tips.map((tip, tipIndex) => (
                        <ListItem key={tipIndex} sx={{ px: 0, py: 0.25 }}>
                          <ListItemIcon sx={{ minWidth: 20 }}>
                            <Box
                              sx={{
                                width: 4,
                                height: 4,
                                borderRadius: '50%',
                                backgroundColor: 'white',
                              }}
                            />
                          </ListItemIcon>
                          <ListItemText>
                            <Typography variant="caption" sx={{ opacity: 0.9 }}>
                              {tip}
                            </Typography>
                          </ListItemText>
                        </ListItem>
                      ))}
                    </List>

                    <Chip
                      label={step.content.metric}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        fontSize: '11px',
                        mb: 2,
                      }}
                    />

                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        onClick={() => handleStepComplete(index)}
                        sx={{
                          backgroundColor: 'rgba(255,255,255,0.2)',
                          color: 'white',
                          '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                        }}
                      >
                        Got it!
                      </Button>
                      {index < tutorialSteps.length - 1 && (
                        <Button
                          size="small"
                          variant="text"
                          onClick={() => setActiveStep(index + 1)}
                          sx={{ color: 'rgba(255,255,255,0.7)' }}
                        >
                          Skip
                        </Button>
                      )}
                    </Box>
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              Master analytics to grow your presence
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                if (onStartTour) onStartTour();
                handleDismiss();
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              Start Interactive Tour
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
