import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  IconButton,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Chip,
  Avatar,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import {
  Close as CloseIcon,
  Link as LinkIcon,
  Share as ShareIcon,
  TrendingUp as TrendingUpIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function LinkManagementGuide({ socialLinks, customLinks, onAddLink }) {
  const [isVisible, setIsVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('linkManagementGuideVisible');
    const hasSeenGuide = localStorage.getItem('hasSeenLinkGuide');
    
    // Show if user hasn't dismissed and either has no links or hasn't seen guide
    const shouldShow = isCardVisible !== 'false' && 
      ((!socialLinks?.length && !customLinks?.length) || hasSeenGuide !== 'true');
    
    setIsVisible(shouldShow);
    
    // Determine current step based on user progress
    if (socialLinks?.length > 0 && customLinks?.length > 0) {
      setCurrentStep(2);
    } else if (socialLinks?.length > 0 || customLinks?.length > 0) {
      setCurrentStep(1);
    } else {
      setCurrentStep(0);
    }
  }, [socialLinks, customLinks]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('linkManagementGuideVisible', 'false');
    localStorage.setItem('hasSeenLinkGuide', 'true');
  };

  const steps = [
    'Add Your First Link',
    'Diversify Your Presence',
    'Optimize & Track'
  ];

  const linkSuggestions = [
    { name: 'LinkedIn', icon: '💼', color: '#0077B5', priority: 'high' },
    { name: 'Instagram', icon: '📸', color: '#E4405F', priority: 'high' },
    { name: 'Twitter', icon: '🐦', color: '#1DA1F2', priority: 'medium' },
    { name: 'Portfolio', icon: '🌐', color: '#6366F1', priority: 'high' },
    { name: 'GitHub', icon: '💻', color: '#333', priority: 'medium' },
  ];

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              Start building your digital presence! Add your most important social media or website link.
            </Typography>
            <List sx={{ py: 0 }}>
              {linkSuggestions.slice(0, 3).map((suggestion, index) => (
                <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <Avatar
                      sx={{
                        width: 24,
                        height: 24,
                        backgroundColor: suggestion.color,
                        fontSize: '12px',
                      }}
                    >
                      {suggestion.icon}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText>
                    <Typography variant="body2">{suggestion.name}</Typography>
                  </ListItemText>
                  <Chip
                    label={suggestion.priority}
                    size="small"
                    color={suggestion.priority === 'high' ? 'primary' : 'default'}
                    sx={{ fontSize: '10px', height: 20 }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              Great start! Now add 2-3 more links to showcase different aspects of your professional life.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {linkSuggestions.map((suggestion, index) => (
                <Chip
                  key={index}
                  label={suggestion.name}
                  size="small"
                  avatar={<span style={{ fontSize: '12px' }}>{suggestion.icon}</span>}
                  sx={{ fontSize: '11px' }}
                />
              ))}
            </Box>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              Perfect! Your link collection is growing. Monitor your analytics to see which links perform best.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />
                <Typography variant="caption">Track clicks</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ShareIcon sx={{ fontSize: 16, color: 'info.main' }} />
                <Typography variant="caption">Share profile</Typography>
              </Box>
            </Box>
          </Box>
        );
      default:
        return null;
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          border: '2px solid',
          borderColor: 'primary.main',
          borderRadius: 2,
          position: 'relative',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LinkIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="div">
              Link Management Guide
            </Typography>
          </Box>

          <Stepper activeStep={currentStep} sx={{ mb: 3 }}>
            {steps.map((label, index) => (
              <Step key={label}>
                <StepLabel
                  StepIconComponent={({ active, completed }) => (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: completed ? 'success.main' : active ? 'primary.main' : 'grey.300',
                        color: 'white',
                        fontSize: '12px',
                      }}
                    >
                      {completed ? <CheckCircleIcon sx={{ fontSize: 16 }} /> : index + 1}
                    </Box>
                  )}
                >
                  <Typography variant="caption">{label}</Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {getStepContent(currentStep)}

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              {socialLinks?.length + customLinks?.length || 0} links added
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => {
                if (onAddLink) onAddLink();
                // Could trigger the add link dialog
              }}
              sx={{ borderRadius: 2 }}
            >
              Add Link
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
