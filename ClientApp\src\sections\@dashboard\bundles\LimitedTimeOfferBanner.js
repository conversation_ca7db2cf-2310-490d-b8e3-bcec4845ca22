import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  LocalOffer as LocalOfferIcon,
  Timer as TimerIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  Flash as FlashIcon,
  Celebration as CelebrationIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function LimitedTimeOfferBanner({ currentPlan, onClaimOffer }) {
  const [isVisible, setIsVisible] = useState(false);
  const [timeLeft, setTimeLeft] = useState({
    hours: 23,
    minutes: 45,
    seconds: 30,
  });
  const [urgencyLevel, setUrgencyLevel] = useState('medium');

  useEffect(() => {
    const isCardVisible = localStorage.getItem('limitedTimeOfferBannerVisible');
    const lastOfferShown = localStorage.getItem('lastOfferShown');
    const now = new Date().getTime();
    const daysSinceLastOffer = lastOfferShown ? (now - parseInt(lastOfferShown)) / (1000 * 60 * 60 * 24) : 999;
    
    const isFreePlan = currentPlan === 'Free' || currentPlan === 'Student';
    
    // Show offer weekly for free users or if they haven't seen it recently
    setIsVisible(isCardVisible !== 'false' && isFreePlan && daysSinceLastOffer > 7);
    
    // Countdown timer
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        let newSeconds = prev.seconds - 1;
        let newMinutes = prev.minutes;
        let newHours = prev.hours;
        
        if (newSeconds < 0) {
          newSeconds = 59;
          newMinutes -= 1;
        }
        
        if (newMinutes < 0) {
          newMinutes = 59;
          newHours -= 1;
        }
        
        if (newHours < 0) {
          // Reset to 24 hours when timer expires
          return { hours: 23, minutes: 59, seconds: 59 };
        }
        
        // Set urgency level based on time remaining
        const totalMinutes = newHours * 60 + newMinutes;
        if (totalMinutes < 60) {
          setUrgencyLevel('high');
        } else if (totalMinutes < 360) {
          setUrgencyLevel('medium');
        } else {
          setUrgencyLevel('low');
        }
        
        return { hours: newHours, minutes: newMinutes, seconds: newSeconds };
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, [currentPlan]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('limitedTimeOfferBannerVisible', 'false');
    localStorage.setItem('lastOfferShown', new Date().getTime().toString());
  };

  const offers = [
    {
      title: '50% OFF Pro Plan',
      originalPrice: '$9',
      discountedPrice: '$4.50',
      savings: 'Save $54/year',
      badge: 'FLASH SALE',
      color: '#FF5722',
    },
    {
      title: '40% OFF Business Plan',
      originalPrice: '$29',
      discountedPrice: '$17.40',
      savings: 'Save $139/year',
      badge: 'LIMITED TIME',
      color: '#9C27B0',
    },
  ];

  const currentOffer = offers[0]; // Could rotate based on user behavior

  const getUrgencyColor = () => {
    switch (urgencyLevel) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#FF9800';
    }
  };

  const getUrgencyMessage = () => {
    switch (urgencyLevel) {
      case 'high': return '🔥 HURRY! Less than 1 hour left!';
      case 'medium': return '⚡ Limited time remaining!';
      case 'low': return '⭐ Special offer available!';
      default: return '⚡ Limited time remaining!';
    }
  };

  const formatTime = (value) => value.toString().padStart(2, '0');

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: `linear-gradient(135deg, ${currentOffer.color} 0%, #FF6B35 100%)`,
          color: 'white',
          position: 'relative',
          borderRadius: 3,
          border: '3px solid #FFD700',
          overflow: 'hidden',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
            zIndex: 2,
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        {/* Animated background elements */}
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(255,215,0,0.3) 0%, transparent 70%)',
            animation: 'pulse 2s ease-in-out infinite',
            '@keyframes pulse': {
              '0%, 100%': { transform: 'scale(1)', opacity: 0.7 },
              '50%': { transform: 'scale(1.2)', opacity: 0.3 },
            },
          }}
        />

        <CardContent sx={{ position: 'relative' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LocalOfferIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div" sx={{ fontWeight: 700 }}>
              {currentOffer.title}
            </Typography>
            <Chip
              label={currentOffer.badge}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: '#FFD700',
                color: 'black',
                fontWeight: 700,
                animation: 'blink 1.5s ease-in-out infinite',
                '@keyframes blink': {
                  '0%, 100%': { opacity: 1 },
                  '50%': { opacity: 0.7 },
                },
              }}
            />
          </Box>

          {/* Urgency Message */}
          <Box sx={{ 
            backgroundColor: getUrgencyColor(), 
            borderRadius: 2, 
            p: 1, 
            mb: 3,
            textAlign: 'center',
          }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              {getUrgencyMessage()}
            </Typography>
          </Box>

          {/* Countdown Timer */}
          <Box sx={{ 
            backgroundColor: 'rgba(255,255,255,0.1)', 
            borderRadius: 3, 
            p: 2, 
            mb: 3,
            textAlign: 'center',
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
              <TimerIcon sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="subtitle2">Offer expires in:</Typography>
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
              {[
                { label: 'Hours', value: timeLeft.hours },
                { label: 'Minutes', value: timeLeft.minutes },
                { label: 'Seconds', value: timeLeft.seconds },
              ].map((unit, index) => (
                <Box key={unit.label} sx={{ textAlign: 'center' }}>
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      fontWeight: 700,
                      color: urgencyLevel === 'high' ? '#FFD700' : 'white',
                      fontFamily: 'monospace',
                    }}
                  >
                    {formatTime(unit.value)}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    {unit.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>

          {/* Pricing */}
          <Box sx={{ 
            backgroundColor: 'rgba(255,255,255,0.1)', 
            borderRadius: 2, 
            p: 2, 
            mb: 3,
            textAlign: 'center',
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2, mb: 1 }}>
              <Typography 
                variant="h4" 
                sx={{ 
                  textDecoration: 'line-through', 
                  opacity: 0.6,
                  fontSize: '1.5rem',
                }}
              >
                {currentOffer.originalPrice}
              </Typography>
              <Typography 
                variant="h3" 
                sx={{ 
                  fontWeight: 700,
                  color: '#FFD700',
                }}
              >
                {currentOffer.discountedPrice}
              </Typography>
              <Typography variant="body2">/month</Typography>
            </Box>
            
            <Chip
              label={currentOffer.savings}
              sx={{
                backgroundColor: '#4CAF50',
                color: 'white',
                fontWeight: 600,
              }}
            />
          </Box>

          {/* Benefits */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, textAlign: 'center' }}>
              What you get instantly:
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
              {[
                '🚀 Unlimited Links',
                '📊 Advanced Analytics', 
                '🎨 Custom Branding',
                '🌐 Custom Domain',
              ].map((benefit, index) => (
                <Chip
                  key={index}
                  label={benefit}
                  size="small"
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontSize: '11px',
                  }}
                />
              ))}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => {
                if (onClaimOffer) onClaimOffer(currentOffer);
                handleDismiss();
              }}
              sx={{
                backgroundColor: '#FFD700',
                color: 'black',
                '&:hover': { backgroundColor: '#FFC107' },
                borderRadius: 3,
                fontWeight: 700,
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                boxShadow: '0 4px 20px rgba(255,215,0,0.4)',
              }}
              startIcon={<CelebrationIcon />}
            >
              Claim This Deal
            </Button>
          </Box>

          <Typography variant="caption" sx={{ 
            display: 'block', 
            textAlign: 'center', 
            mt: 2, 
            opacity: 0.8 
          }}>
            💳 No commitment • Cancel anytime • 30-day money-back guarantee
          </Typography>
        </CardContent>
      </Card>
    </motion.div>
  );
}
