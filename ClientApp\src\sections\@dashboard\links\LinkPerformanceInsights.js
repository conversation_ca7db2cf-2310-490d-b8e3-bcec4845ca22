import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>Content,
  Ty<PERSON><PERSON>,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Insights as InsightsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  Visibility as VisibilityIcon,
  Mouse as MouseIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function LinkPerformanceInsights({ links, analytics, onViewAnalytics }) {
  const [isVisible, setIsVisible] = useState(false);
  const [insights, setInsights] = useState([]);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('linkPerformanceInsightsVisible');
    const hasAnalytics = analytics && Object.keys(analytics).length > 0;
    const hasLinks = links && links.length > 0;
    
    setIsVisible(isCardVisible !== 'false' && hasLinks && hasAnalytics);
    generateInsights();
  }, [links, analytics]);

  const generateInsights = () => {
    if (!analytics || !links) return;

    const generatedInsights = [];
    
    // Mock analytics data structure - replace with real analytics
    const mockAnalytics = {
      totalClicks: 156,
      totalViews: 1240,
      clickRate: 12.6,
      topPerformer: links[0]?.title || 'LinkedIn',
      worstPerformer: links[links.length - 1]?.title || 'GitHub',
      peakHour: '2 PM',
      growthRate: 23,
    };

    // Generate insights based on performance
    if (mockAnalytics.clickRate > 10) {
      generatedInsights.push({
        type: 'positive',
        icon: <TrendingUpIcon sx={{ color: '#4CAF50' }} />,
        title: 'Great Click Rate!',
        description: `Your ${mockAnalytics.clickRate}% click rate is above average`,
        action: 'Keep it up',
        color: '#4CAF50',
      });
    }

    if (mockAnalytics.topPerformer) {
      generatedInsights.push({
        type: 'info',
        icon: <StarIcon sx={{ color: '#FF9800' }} />,
        title: 'Top Performer',
        description: `"${mockAnalytics.topPerformer}" gets the most clicks`,
        action: 'Analyze why',
        color: '#FF9800',
      });
    }

    if (mockAnalytics.peakHour) {
      generatedInsights.push({
        type: 'tip',
        icon: <ScheduleIcon sx={{ color: '#2196F3' }} />,
        title: 'Peak Activity',
        description: `Most clicks happen around ${mockAnalytics.peakHour}`,
        action: 'Post then',
        color: '#2196F3',
      });
    }

    if (links.length > 5) {
      generatedInsights.push({
        type: 'warning',
        icon: <SpeedIcon sx={{ color: '#FF5722' }} />,
        title: 'Too Many Links?',
        description: 'Consider prioritizing your top 5 links for better performance',
        action: 'Optimize',
        color: '#FF5722',
      });
    }

    setInsights(generatedInsights);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('linkPerformanceInsightsVisible', 'false');
  };

  const performanceMetrics = [
    {
      label: 'Total Clicks',
      value: '156',
      change: '+23%',
      positive: true,
      icon: <MouseIcon />,
    },
    {
      label: 'Profile Views',
      value: '1.2K',
      change: '+12%',
      positive: true,
      icon: <VisibilityIcon />,
    },
    {
      label: 'Click Rate',
      value: '12.6%',
      change: '+2.1%',
      positive: true,
      icon: <TrendingUpIcon />,
    },
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <InsightsIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Link Performance Insights
            </Typography>
            <Chip
              label="Last 7 days"
              size="small"
              sx={{
                ml: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
          </Box>

          {/* Performance Metrics */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            {performanceMetrics.map((metric, index) => (
              <Box
                key={index}
                sx={{
                  flex: 1,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  borderRadius: 2,
                  p: 1.5,
                  textAlign: 'center',
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 0.5 }}>
                  {React.cloneElement(metric.icon, { sx: { fontSize: 20 } })}
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {metric.value}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {metric.label}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 0.5 }}>
                  {metric.positive ? (
                    <TrendingUpIcon sx={{ fontSize: 12, mr: 0.5 }} />
                  ) : (
                    <TrendingDownIcon sx={{ fontSize: 12, mr: 0.5 }} />
                  )}
                  <Typography variant="caption">{metric.change}</Typography>
                </Box>
              </Box>
            ))}
          </Box>

          <Divider sx={{ backgroundColor: 'rgba(255,255,255,0.2)', mb: 2 }} />

          {/* Insights */}
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Smart Insights
          </Typography>

          <List sx={{ py: 0 }}>
            {insights.slice(0, 3).map((insight, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ListItem
                  sx={{
                    px: 0,
                    py: 1,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    borderRadius: 2,
                    mb: 1,
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Avatar
                      sx={{
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        width: 32,
                        height: 32,
                      }}
                    >
                      {React.cloneElement(insight.icon, { sx: { fontSize: 16 } })}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {insight.title}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '13px' }}>
                      {insight.description}
                    </Typography>
                  </ListItemText>
                  <Button
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                      minWidth: 'auto',
                      px: 2,
                      fontSize: '11px',
                    }}
                  >
                    {insight.action}
                  </Button>
                </ListItem>
              </motion.div>
            ))}
          </List>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              Data updates every hour
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                if (onViewAnalytics) onViewAnalytics();
              }}
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                borderRadius: 2,
              }}
            >
              View Full Analytics
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}


