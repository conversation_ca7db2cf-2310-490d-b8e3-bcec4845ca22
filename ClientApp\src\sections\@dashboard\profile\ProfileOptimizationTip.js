import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  IconButton,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Chip,
  Button,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function ProfileOptimizationTip({ profile, onOptimizationComplete }) {
  const [isVisible, setIsVisible] = useState(false);
  const [completionScore, setCompletionScore] = useState(0);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('profileOptimizationTipVisible');
    setIsVisible(isCardVisible !== 'false');
    
    // Calculate profile completion score
    calculateCompletionScore();
  }, [profile]);

  const calculateCompletionScore = () => {
    let score = 0;
    const checks = [
      profile?.occupation,
      profile?.firstName && profile?.lastName,
      profile?.country,
      profile?.bio,
      profile?.profilePicture,
    ];
    
    score = (checks.filter(Boolean).length / checks.length) * 100;
    setCompletionScore(Math.round(score));
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('profileOptimizationTipVisible', 'false');
  };

  const optimizationTips = [
    {
      icon: <StarIcon sx={{ color: '#FFD700', fontSize: 20 }} />,
      text: 'Add a professional bio to increase profile views by 40%',
      completed: !!profile?.bio,
    },
    {
      icon: <VisibilityIcon sx={{ color: '#4CAF50', fontSize: 20 }} />,
      text: 'Upload a profile picture for better engagement',
      completed: !!profile?.profilePicture,
    },
    {
      icon: <TrendingUpIcon sx={{ color: '#2196F3', fontSize: 20 }} />,
      text: 'Complete your location for local networking',
      completed: !!profile?.country,
    },
  ];

  if (!isVisible || completionScore >= 80) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'visible',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent sx={{ pt: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TrendingUpIcon sx={{ mr: 1, fontSize: 28 }} />
            <Typography variant="h6" component="div">
              Boost Your Profile Power!
            </Typography>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Profile Completion</Typography>
              <Typography variant="body2">{completionScore}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={completionScore}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: completionScore > 60 ? '#4CAF50' : '#FFC107',
                  borderRadius: 4,
                },
              }}
            />
          </Box>

          <List sx={{ py: 0 }}>
            {optimizationTips.map((tip, index) => (
              <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 32 }}>
                  {tip.completed ? (
                    <CheckCircleOutlineIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                  ) : (
                    tip.icon
                  )}
                </ListItemIcon>
                <ListItemText>
                  <Typography
                    variant="body2"
                    sx={{
                      textDecoration: tip.completed ? 'line-through' : 'none',
                      opacity: tip.completed ? 0.7 : 1,
                    }}
                  >
                    {tip.text}
                  </Typography>
                </ListItemText>
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Chip
              label={`${optimizationTips.filter(tip => tip.completed).length}/${optimizationTips.length} Complete`}
              size="small"
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
            <Button
              variant="contained"
              size="small"
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
              }}
              onClick={() => {
                if (onOptimizationComplete) onOptimizationComplete();
                // Scroll to first incomplete section
                const firstIncomplete = optimizationTips.find(tip => !tip.completed);
                if (firstIncomplete) {
                  // Could trigger scroll to relevant form section
                }
              }}
            >
              Optimize Now
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}
