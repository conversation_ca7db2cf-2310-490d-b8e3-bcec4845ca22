import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Divider,
  Badge,
} from '@mui/material';
import {
  Close as CloseIcon,
  EmojiEvents as EmojiEventsIcon,
  TrendingUp as TrendingUpIcon,
  Target as TargetIcon,
  Star as StarIcon,
  Timeline as TimelineIcon,
  Celebration as CelebrationIcon,
  Flag as FlagIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function EngagementTrackingMotivator({ currentMetrics, goals, onSetGoal }) {
  const [isVisible, setIsVisible] = useState(false);
  const [achievements, setAchievements] = useState([]);
  const [currentGoal, setCurrentGoal] = useState(null);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('engagementTrackingMotivatorVisible');
    const lastShown = localStorage.getItem('motivatorLastShown');
    const now = new Date().getTime();
    const daysSinceLastShown = lastShown ? (now - parseInt(lastShown)) / (1000 * 60 * 60 * 24) : 999;
    
    // Show weekly or when user achieves something
    setIsVisible(isCardVisible !== 'false' && daysSinceLastShown > 3);
    
    generateAchievements();
    setCurrentGoal(goals?.current || getNextGoal());
  }, [currentMetrics, goals]);

  const generateAchievements = () => {
    const mockMetrics = {
      views: 245,
      clicks: 34,
      profileCompleteness: 85,
      linksAdded: 6,
      daysActive: 12,
    };

    const achievementsList = [];

    // Views milestones
    if (mockMetrics.views >= 100) {
      achievementsList.push({
        id: 'views_100',
        title: 'Century Club! 💯',
        description: 'Reached 100+ profile views',
        icon: <VisibilityIcon sx={{ color: '#4CAF50' }} />,
        unlocked: true,
        rarity: 'common',
      });
    }

    if (mockMetrics.views >= 500) {
      achievementsList.push({
        id: 'views_500',
        title: 'Rising Star! ⭐',
        description: 'Achieved 500+ profile views',
        icon: <StarIcon sx={{ color: '#FFD700' }} />,
        unlocked: true,
        rarity: 'rare',
      });
    }

    // Engagement milestones
    if (mockMetrics.clicks >= 25) {
      achievementsList.push({
        id: 'clicks_25',
        title: 'Click Magnet! 🧲',
        description: 'Generated 25+ link clicks',
        icon: <TrendingUpIcon sx={{ color: '#2196F3' }} />,
        unlocked: true,
        rarity: 'common',
      });
    }

    // Profile completion
    if (mockMetrics.profileCompleteness >= 80) {
      achievementsList.push({
        id: 'profile_complete',
        title: 'Profile Master! 🎯',
        description: 'Profile 80%+ complete',
        icon: <TargetIcon sx={{ color: '#9C27B0' }} />,
        unlocked: true,
        rarity: 'uncommon',
      });
    }

    setAchievements(achievementsList);
  };

  const getNextGoal = () => {
    const mockMetrics = {
      views: 245,
      clicks: 34,
    };

    if (mockMetrics.views < 500) {
      return {
        type: 'views',
        target: 500,
        current: mockMetrics.views,
        title: 'Reach 500 Views',
        reward: 'Rising Star Badge',
        timeframe: '2 weeks',
      };
    }

    if (mockMetrics.clicks < 50) {
      return {
        type: 'clicks',
        target: 50,
        current: mockMetrics.clicks,
        title: 'Get 50 Link Clicks',
        reward: 'Engagement Expert Badge',
        timeframe: '1 week',
      };
    }

    return {
      type: 'views',
      target: 1000,
      current: mockMetrics.views,
      title: 'Reach 1K Views',
      reward: 'Influencer Status',
      timeframe: '1 month',
    };
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('engagementTrackingMotivatorVisible', 'false');
    localStorage.setItem('motivatorLastShown', new Date().getTime().toString());
  };

  const progressPercentage = currentGoal ? 
    Math.min((currentGoal.current / currentGoal.target) * 100, 100) : 0;

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'common': return '#4CAF50';
      case 'uncommon': return '#2196F3';
      case 'rare': return '#9C27B0';
      case 'epic': return '#FF9800';
      case 'legendary': return '#F44336';
      default: return '#757575';
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
          position: 'relative',
          borderRadius: 3,
          border: '2px solid #FFD700',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <EmojiEventsIcon sx={{ mr: 1, color: '#FFD700' }} />
            <Typography variant="h6" component="div">
              Your Progress Journey
            </Typography>
            <Chip
              label="Weekly Update"
              size="small"
              sx={{
                ml: 1,
                backgroundColor: '#FFD700',
                color: 'white',
              }}
            />
          </Box>

          {/* Current Goal */}
          {currentGoal && (
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FlagIcon sx={{ mr: 1, color: '#FF5722' }} />
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Current Goal: {currentGoal.title}
                </Typography>
              </Box>
              
              <Box sx={{ 
                backgroundColor: 'rgba(255,255,255,0.7)', 
                borderRadius: 2, 
                p: 2, 
                mb: 2 
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">
                    {currentGoal.current} / {currentGoal.target} {currentGoal.type}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {Math.round(progressPercentage)}%
                  </Typography>
                </Box>
                
                <Box sx={{ position: 'relative', mb: 2 }}>
                  <CircularProgress
                    variant="determinate"
                    value={progressPercentage}
                    size={60}
                    thickness={6}
                    sx={{
                      color: progressPercentage > 75 ? '#4CAF50' : progressPercentage > 50 ? '#FF9800' : '#2196F3',
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                      {Math.round(progressPercentage)}%
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    🏆 Reward: {currentGoal.reward}
                  </Typography>
                  <Chip
                    label={currentGoal.timeframe}
                    size="small"
                    sx={{ fontSize: '10px', height: 20 }}
                  />
                </Box>
              </Box>
            </Box>
          )}

          <Divider sx={{ my: 2 }} />

          {/* Recent Achievements */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CelebrationIcon sx={{ mr: 1, color: '#9C27B0' }} />
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                Recent Achievements
              </Typography>
            </Box>

            <List sx={{ py: 0 }}>
              {achievements.slice(0, 3).map((achievement, index) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ListItem
                    sx={{
                      px: 0,
                      py: 1,
                      backgroundColor: 'rgba(255,255,255,0.5)',
                      borderRadius: 2,
                      mb: 1,
                      border: `2px solid ${getRarityColor(achievement.rarity)}`,
                    }}
                  >
                    <ListItemIcon>
                      <Badge
                        badgeContent="✨"
                        sx={{
                          '& .MuiBadge-badge': {
                            fontSize: '10px',
                            minWidth: 16,
                            height: 16,
                          },
                        }}
                      >
                        <Avatar
                          sx={{
                            backgroundColor: `${getRarityColor(achievement.rarity)}20`,
                            width: 40,
                            height: 40,
                          }}
                        >
                          {achievement.icon}
                        </Avatar>
                      </Badge>
                    </ListItemIcon>
                    <ListItemText>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {achievement.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {achievement.description}
                      </Typography>
                    </ListItemText>
                    <Chip
                      label={achievement.rarity}
                      size="small"
                      sx={{
                        backgroundColor: getRarityColor(achievement.rarity),
                        color: 'white',
                        fontSize: '10px',
                        height: 20,
                        textTransform: 'capitalize',
                      }}
                    />
                  </ListItem>
                </motion.div>
              ))}
            </List>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Keep going! You're doing great! 🚀
            </Typography>
            <Button
              variant="contained"
              size="small"
              onClick={() => {
                if (onSetGoal) onSetGoal();
              }}
              sx={{
                backgroundColor: '#FFD700',
                color: 'black',
                '&:hover': { backgroundColor: '#FFC107' },
                borderRadius: 2,
                fontWeight: 600,
              }}
            >
              Set New Goal
            </Button>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}


