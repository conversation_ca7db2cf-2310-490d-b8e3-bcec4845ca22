import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Button,
  Chip,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Switch,
  FormControlLabel,
  Slider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Close as CloseIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Analytics as AnalyticsIcon,
  Public as PublicIcon,
  CheckCircle as CheckCircleIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export default function FeatureConfigurationWizard({ currentConfig, onConfigUpdate }) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [config, setConfig] = useState({});
  const [isQuickSetup, setIsQuickSetup] = useState(false);

  useEffect(() => {
    const isCardVisible = localStorage.getItem('featureConfigurationWizardVisible');
    const hasDefaultConfig = !currentConfig || Object.keys(currentConfig).length === 0;
    const lastConfigUpdate = localStorage.getItem('lastConfigUpdate');
    const now = new Date().getTime();
    const daysSinceUpdate = lastConfigUpdate ? (now - parseInt(lastConfigUpdate)) / (1000 * 60 * 60 * 24) : 999;
    
    // Show for new users or if config hasn't been updated in 30 days
    setIsVisible(isCardVisible !== 'false' && (hasDefaultConfig || daysSinceUpdate > 30));
    
    // Initialize config with defaults
    setConfig({
      notifications: {
        email: true,
        push: false,
        frequency: 'weekly',
      },
      theme: {
        mode: 'light',
        primaryColor: '#2196F3',
        customization: 'basic',
      },
      analytics: {
        enabled: true,
        detailed: false,
        sharing: 'private',
      },
      privacy: {
        profileVisibility: 'public',
        showAnalytics: false,
        allowIndexing: true,
      },
      ...currentConfig,
    });
  }, [currentConfig]);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('featureConfigurationWizardVisible', 'false');
    localStorage.setItem('lastConfigUpdate', new Date().getTime().toString());
  };

  const handleNext = () => {
    setActiveStep(prev => Math.min(prev + 1, configurationSteps.length - 1));
  };

  const handleBack = () => {
    setActiveStep(prev => Math.max(prev - 1, 0));
  };

  const handleConfigChange = (category, key, value) => {
    setConfig(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }));
  };

  const handleQuickSetup = () => {
    const quickConfig = {
      notifications: { email: true, push: true, frequency: 'daily' },
      theme: { mode: 'light', primaryColor: '#2196F3', customization: 'advanced' },
      analytics: { enabled: true, detailed: true, sharing: 'private' },
      privacy: { profileVisibility: 'public', showAnalytics: false, allowIndexing: true },
    };
    
    setConfig(quickConfig);
    setIsQuickSetup(true);
    
    if (onConfigUpdate) {
      onConfigUpdate(quickConfig);
    }
    
    setTimeout(() => {
      handleDismiss();
    }, 2000);
  };

  const configurationSteps = [
    {
      label: 'Notifications',
      icon: <NotificationsIcon sx={{ color: '#FF9800' }} />,
      title: 'Stay Informed',
      description: 'Configure how and when you want to receive updates',
      content: (
        <Box sx={{ mt: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={config.notifications?.email || false}
                onChange={(e) => handleConfigChange('notifications', 'email', e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: 'white' } }}
              />
            }
            label="Email notifications"
            sx={{ color: 'white', mb: 2, display: 'block' }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={config.notifications?.push || false}
                onChange={(e) => handleConfigChange('notifications', 'push', e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: 'white' } }}
              />
            }
            label="Push notifications"
            sx={{ color: 'white', mb: 2, display: 'block' }}
          />
          
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel sx={{ color: 'white' }}>Frequency</InputLabel>
            <Select
              value={config.notifications?.frequency || 'weekly'}
              onChange={(e) => handleConfigChange('notifications', 'frequency', e.target.value)}
              sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'white' } }}
            >
              <MenuItem value="daily">Daily</MenuItem>
              <MenuItem value="weekly">Weekly</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
            </Select>
          </FormControl>
        </Box>
      ),
    },
    {
      label: 'Appearance',
      icon: <PaletteIcon sx={{ color: '#9C27B0' }} />,
      title: 'Personalize Your Look',
      description: 'Customize the visual appearance of your profile',
      content: (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
            Theme Mode
          </Typography>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <Select
              value={config.theme?.mode || 'light'}
              onChange={(e) => handleConfigChange('theme', 'mode', e.target.value)}
              sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'white' } }}
            >
              <MenuItem value="light">Light</MenuItem>
              <MenuItem value="dark">Dark</MenuItem>
              <MenuItem value="auto">Auto</MenuItem>
            </Select>
          </FormControl>
          
          <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
            Customization Level
          </Typography>
          <FormControl fullWidth>
            <Select
              value={config.theme?.customization || 'basic'}
              onChange={(e) => handleConfigChange('theme', 'customization', e.target.value)}
              sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'white' } }}
            >
              <MenuItem value="basic">Basic</MenuItem>
              <MenuItem value="advanced">Advanced</MenuItem>
              <MenuItem value="custom">Custom</MenuItem>
            </Select>
          </FormControl>
        </Box>
      ),
    },
    {
      label: 'Analytics',
      icon: <AnalyticsIcon sx={{ color: '#2196F3' }} />,
      title: 'Track Your Growth',
      description: 'Set up analytics to understand your audience',
      content: (
        <Box sx={{ mt: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={config.analytics?.enabled || false}
                onChange={(e) => handleConfigChange('analytics', 'enabled', e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: 'white' } }}
              />
            }
            label="Enable analytics tracking"
            sx={{ color: 'white', mb: 2, display: 'block' }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={config.analytics?.detailed || false}
                onChange={(e) => handleConfigChange('analytics', 'detailed', e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: 'white' } }}
              />
            }
            label="Detailed analytics"
            sx={{ color: 'white', mb: 2, display: 'block' }}
          />
          
          <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
            Data Sharing
          </Typography>
          <FormControl fullWidth>
            <Select
              value={config.analytics?.sharing || 'private'}
              onChange={(e) => handleConfigChange('analytics', 'sharing', e.target.value)}
              sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'white' } }}
            >
              <MenuItem value="private">Private</MenuItem>
              <MenuItem value="anonymous">Anonymous</MenuItem>
              <MenuItem value="public">Public</MenuItem>
            </Select>
          </FormControl>
        </Box>
      ),
    },
    {
      label: 'Privacy',
      icon: <PublicIcon sx={{ color: '#4CAF50' }} />,
      title: 'Control Your Privacy',
      description: 'Manage who can see your profile and data',
      content: (
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
            Profile Visibility
          </Typography>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <Select
              value={config.privacy?.profileVisibility || 'public'}
              onChange={(e) => handleConfigChange('privacy', 'profileVisibility', e.target.value)}
              sx={{ color: 'white', '& .MuiOutlinedInput-notchedOutline': { borderColor: 'white' } }}
            >
              <MenuItem value="public">Public</MenuItem>
              <MenuItem value="unlisted">Unlisted</MenuItem>
              <MenuItem value="private">Private</MenuItem>
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={config.privacy?.allowIndexing || false}
                onChange={(e) => handleConfigChange('privacy', 'allowIndexing', e.target.checked)}
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: 'white' } }}
              />
            }
            label="Allow search engine indexing"
            sx={{ color: 'white', mb: 2, display: 'block' }}
          />
        </Box>
      ),
    },
  ];

  const currentStepData = configurationSteps[activeStep];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          borderRadius: 3,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
            '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
          }}
          onClick={handleDismiss}
        >
          <CloseIcon />
        </IconButton>

        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <SettingsIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="div">
              Feature Configuration Wizard
            </Typography>
            <Chip
              label={`Step ${activeStep + 1}/${configurationSteps.length}`}
              size="small"
              sx={{
                ml: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white',
              }}
            />
          </Box>

          {isQuickSetup ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <CheckCircleIcon sx={{ fontSize: 64, color: '#4CAF50', mb: 2 }} />
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                  Configuration Complete!
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Your settings have been optimized for the best experience.
                </Typography>
              </Box>
            </motion.div>
          ) : (
            <>
              {/* Quick Setup Option */}
              <Box sx={{ 
                backgroundColor: 'rgba(255,255,255,0.1)', 
                borderRadius: 2, 
                p: 2, 
                mb: 3,
                textAlign: 'center',
              }}>
                <AutoAwesomeIcon sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Quick Setup Available
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
                  Apply recommended settings instantly
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  onClick={handleQuickSetup}
                  sx={{
                    backgroundColor: '#FFD700',
                    color: 'black',
                    '&:hover': { backgroundColor: '#FFC107' },
                    borderRadius: 2,
                    fontWeight: 600,
                  }}
                >
                  Use Quick Setup
                </Button>
              </Box>

              {/* Step Content */}
              <Box sx={{ 
                backgroundColor: 'rgba(255,255,255,0.1)', 
                borderRadius: 3, 
                p: 3, 
                mb: 3 
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      mr: 2,
                      width: 40,
                      height: 40,
                    }}
                  >
                    {currentStepData.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {currentStepData.title}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      {currentStepData.description}
                    </Typography>
                  </Box>
                </Box>

                {currentStepData.content}
              </Box>

              {/* Navigation */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  sx={{ color: 'white' }}
                >
                  Back
                </Button>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {configurationSteps.map((_, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: index === activeStep ? 'white' : 'rgba(255,255,255,0.3)',
                      }}
                    />
                  ))}
                </Box>

                <Button
                  onClick={activeStep === configurationSteps.length - 1 ? () => {
                    if (onConfigUpdate) onConfigUpdate(config);
                    handleDismiss();
                  } : handleNext}
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' },
                    borderRadius: 2,
                  }}
                >
                  {activeStep === configurationSteps.length - 1 ? 'Finish' : 'Next'}
                </Button>
              </Box>
            </>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
